# Project Context & Development History

## Project Overview
Keitaro postback system with Google Ads conversion tracking and random subdomain postbacks.

## Task History & Sources

### Original Task
- **Date**: 2025-01-09
- **Objective**: Create script for Keitaro S2S postback integration with Google Ads
- **Key Requirements**:
  - <PERSON>le `{sub_id_10}` parameter substitution
  - Send postbacks to `https://url.com?=true&sub_id_10={value}`
  - Generate Google Ads tracking code dynamically
  - Store values in localStorage
  - Use current domain for subdomain generation via window.location.hostname

### Documentation Sources
- **Keitaro S2S Postback**: https://docs.keitaro.io/ru/campaigns-and-streams/s2s-postback.html
- **Google Ads Conversion Tracking**: Standard gtag.js implementation
- **URL Parameter Handling**: Custom JavaScript functions

### Key Updates & Iterations

#### Version 1.0 - Initial Implementation
- Basic postback functionality
- Simple Google Ads integration
- Single domain postbacks

#### Version 2.0 - Enhanced Features
- **Date**: 2025-01-09
- **Changes**:
  - Split `sub_id_10` by "/" into `aw` and `conv_id` components
  - Implement random subdomain generation for postbacks using window.location.hostname
  - Update Google Ads code structure:
    - Load gtag.js with `aw` portion only
    - Send conversion event with full `sub_id_10`
  - Enhanced error handling and logging

#### Version 2.1 - Code Cleanup
- **Date**: 2025-01-09
- **Changes**:
  - Translated all comments to English
  - Removed outdated/irrelevant comments
  - Improved code readability
  - Project structure cleanup

#### Version 2.2 - Hostname Dynamic Detection
- **Date**: 2025-01-09
- **Changes**:
  - Fixed hardcoded domain.com to use window.location.hostname
  - Updated both postback and tracking pixel generation
  - System now automatically detects deployment domain
  - Updated documentation with proper examples

#### Version 2.3 - Single File with Iframe Random Subdomain
- **Date**: 2025-01-09
- **Changes**:
  - Consolidated to single working file: thanks-gads.html
  - Removed gads-conversion.html dependency
  - Implemented iframe-based Google Ads conversion with random subdomain spoofing
  - Each conversion appears to Google Ads as coming from different random subdomain
  - Added hostname override in iframe to simulate random subdomain origin

#### Version 2.4 - Deployment Configuration
- **Date**: 2025-01-09
- **Changes**:
  - Added netlify.toml and _redirects for Netlify deployment
  - Simplified deployment guides for Netlify, Coolify, and aaPanel
  - Removed complex server configurations, focusing on simple GitHub integration
  - Updated guides to use generated domains instead of requiring custom domains
  - Added deployment configuration files for one-click deployment

#### Version 2.5 - Dynamic Conversion Value Support
- **Date**: 2025-01-09
- **Changes**:
  - Added support for dynamic conversion value via URL parameters
  - Added currency parameter support (value and currency)
  - Implemented 'none' value option to disable value tracking
  - Added detailed logging for conversion value and currency
  - Updated documentation with examples and code modification instructions
  - Added commented code option for disabling value tracking by default

## Technical Implementation

### Core Components
1. **URL Parameter Parsing**: `getUrlParameter()`, `getAllUrlParams()`
2. **Random Subdomain Generation**: `generateRandomSubdomain()`
3. **Google Ads Integration**: Iframe-based with hostname spoofing for random subdomain origin
4. **Postback System**: Hidden iframe method with random subdomains
5. **State Management**: localStorage for parameter persistence
6. **Subdomain Spoofing**: Object.defineProperty to override window.location.hostname in iframe

### File Structure
```
postbacker/
├── .gitignore
├── CONTEXT.md           # This file
├── README.md            # General project documentation
├── netlify.toml         # Netlify deployment configuration
├── _redirects           # Netlify redirects configuration
└── gads/
    ├── conversion-tracker.html  # Main tracker with subdomain generation
    ├── conversion-sender.html   # Iframe-based conversion sender for subdomains
    ├── thanks-gads.html        # Original single-file implementation
    ├── README.md               # Complete system documentation with diagrams
    ├── keitaro-flow.md         # Setup and flow documentation
    ├── netlify.md              # Netlify deployment guide
    ├── coolify.md              # Coolify deployment guide
    ├── vercel.md               # Vercel deployment guide
    ├── github-pages.md         # GitHub Pages + Cloudflare guide
    ├── aapanel.md              # aaPanel deployment guide
    └── HOSTING_GUIDE.md        # General deployment documentation
```

## Latest Updates (2025-07-12)

### Multi-Subdomain System Implementation
- **New Architecture**: Separated into `conversion-tracker.html` and `conversion-sender.html`
- **Wildcard Domain Support**: Full Cloudflare wildcard integration
- **Enhanced Documentation**: Complete guides for multiple hosting platforms
- **Deployment Options**: Coolify, Vercel, GitHub Pages, Netlify support

### Key Improvements
1. **Modular Design**: Tracker generates subdomains, sender handles conversions
2. **Cloudflare Integration**: Wildcard SSL and DNS support
3. **Multiple Hosting Guides**: Platform-specific deployment instructions
4. **Architecture Diagrams**: Mermaid diagrams showing system flow
5. **Comprehensive Testing**: Manual testing procedures and troubleshooting

### Current System Flow
```
User → conversion-tracker.html → Generate random subdomain → Create iframe →
abc123.domain.com/conversion-sender.html → Load gtag → Send conversion to Google Ads
```

## Development Guidelines

### Code Standards
- All comments in English
- Consistent variable naming
- Proper error handling and logging
- URL parameter encoding for security

### Testing Requirements
- Test with full Google Ads Conversion ID format: `AW-XXXXXXXXX/CONVERSION_LABEL`
- Verify random subdomain generation
- Check localStorage persistence
- Validate postback URL generation

### Future Development Ideas
- [ ] Add support for multiple conversion types
- [ ] Implement conversion value tracking
- [ ] Add error reporting mechanism
- [ ] Create admin dashboard for monitoring
- [ ] Add support for other ad networks

### Deployment Options
- **Netlify**: Static hosting with automatic GitHub deployment
- **Coolify**: Self-hosted solution with Docker support on VPS
- **Vercel**: Static hosting with wildcard domain support
- **GitHub Pages + Cloudflare**: Free hosting with wildcard SSL
- **aaPanel**: Traditional web hosting panel for VPS/dedicated servers

## Security Considerations
- All URL parameters are encoded with `encodeURIComponent()`
- Random subdomain generation prevents predictable patterns
- HTTPS enforcement for all external requests
- No sensitive data in localStorage

## Deployment Notes
- Requires HTTPS hosting
- Compatible with static hosting (Netlify, Vercel, GitHub Pages)
- No server-side dependencies
- Works with any domain structure

## Contact & Support
- Original implementation: Claude Code session 2025-01-09
- Multi-subdomain system: Claude Code session 2025-07-12
- For issues: Check console logs and validate Conversion ID format
- Testing: Use browser developer tools to monitor network requests

## Version History
- **v1.0** (2025-01-09): Single-file implementation with basic subdomain postbacks
- **v2.0** (2025-07-12): Multi-file architecture with wildcard domain support
  - Separated tracker and sender components
  - Added comprehensive hosting guides
  - Implemented Cloudflare wildcard integration
  - Added architecture documentation with diagrams