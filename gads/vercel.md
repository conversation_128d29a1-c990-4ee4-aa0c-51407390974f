# Деплой на Vercel

## 🚀 Налаштування проєкту на Vercel

### Крок 1: Підключення GitHub

1. **Відкрийте Vercel Dashboard**
   ```
   https://vercel.com/dashboard
   ```

2. **Імпорт проєкту**
   - Натисніть `New Project`
   - Виберіть GitHub репозиторій `postbacker`
   - Натисніть `Import`

### Крок 2: Налаштування деплою

1. **Build Settings**
   ```
   Framework Preset: Other
   Root Directory: gads/
   Build Command: (залишити порожнім)
   Output Directory: ./
   Install Command: (залишити порожнім)
   ```

2. **Environment Variables**
   ```
   Не потрібні для статичних файлів
   ```

### Крок 3: Налаштування кастомного домену

1. **Додайте домен**
   - Settings → Domains
   - Add Domain: `yourdomain.com`
   - Add Domain: `*.yourdomain.com` (wildcard)

2. **DNS налаштування**
   У вашому DNS провайдері:
   ```
   Type: A
   Name: @
   Value: 76.76.19.19

   Type: CNAME
   Name: *
   Value: cname.vercel-dns.com
   ```

### Крок 4: SSL сертифікати

Vercel автоматично створює SSL сертифікати для:
- Основного домену
- Wildcard субдоменів

### Крок 5: Деплой

1. **Автоматичний деплой**
   ```bash
   git add .
   git commit -m "Deploy to Vercel"
   git push origin main
   ```

2. **Vercel автоматично деплоїть зміни**

## 🧪 Тестування

### Основний домен:
```
https://yourdomain.com/conversion-tracker.html?sub_id_10=AW-123456789/TestLabel&value=100&currency=USD
```

### Wildcard субдомени:
```
https://abc123.yourdomain.com/conversion-sender.html
https://def456.yourdomain.com/conversion-sender.html
```

## ⚙️ Vercel Configuration

Створіть файл `vercel.json` в корені проєкту:

```json
{
  "version": 2,
  "builds": [
    {
      "src": "gads/**",
      "use": "@vercel/static"
    }
  ],
  "routes": [
    {
      "src": "/(.*)",
      "dest": "/gads/$1"
    }
  ],
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "Access-Control-Allow-Origin",
          "value": "*"
        }
      ]
    }
  ]
}
```

## ⚠️ Troubleshooting

### Проблема: Wildcard домен не працює
- Переконайтеся що додали `*.yourdomain.com` в Domains
- Перевірте DNS записи
- Зачекайте до 24 годин для поширення DNS

### Проблема: SSL помилки
- Vercel автоматично створює SSL
- Перевірте статус в Settings → Domains

### Логи деплою:
- Functions → View Function Logs
- Deployments → View Build Logs

## 💰 Ліміти Vercel

### Безкоштовний план:
- 100GB bandwidth/місяць
- Необмежені деплої
- Wildcard домени ✅

### Pro план ($20/місяць):
- 1TB bandwidth/місяць
- Пріоритетна підтримка

---

**Готово!** Система працює на Vercel з wildcard субдоменами.
