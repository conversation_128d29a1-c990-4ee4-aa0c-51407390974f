# Coolify Deployment

## Швидкий деплой

### Крок 1: Деплой в Coolify

1. **Applications** → **New Application**
2. **Type:** Static Site
3. **Repository:** твій GitHub репозиторій
4. **Branch:** main
5. **Build Command:** `echo 'Static site'`
6. **Publish Directory:** `gads`
7. **Domain:** `monegeek.store` (або залиш пустим для автоматичного)
8. **Deploy**

### Крок 2: DNS (якщо свій домен)

У свого провайдера домену:
```
A @ твій-сервер-ip
A * твій-сервер-ip
```

## Готово

- Push в GitHub → автоматичний деплой
- Отримаєш URL або свій домен
- `https://твій-домен/thanks-gads.html` працює

### Postback URL для Keitaro

```
https://твій-домен/thanks-gads.html?sub_id_10={sub_id_10}
```