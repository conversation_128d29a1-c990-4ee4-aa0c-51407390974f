# Гід по хостингу і використанню скрипта

## Огляд

Цей скрипт призначений для обробки постбеків з Keitaro та відправки конверсій в Google Ads з динамічними параметрами.

**Основні функції:**
- Автоматичне розділення Google Ads Conversion ID на частини
- Відправка постбеків з рандомними піддоменами
- Збереження параметрів в localStorage
- Автоматичне перенаправлення з параметрами

## Структура файлів

```
kz-onlybot-gray/
├── thanks-gads.html     # Головна сторінка з Google Ads трекінгом та постбеками
└── HOSTING_GUIDE.md     # Цей документ
```

## Налаштування хостингу

### 1. Вимоги до хостингу
- Підтримка HTTPS (обов'язково для Google Ads)
- Можливість завантаження HTML файлів
- Немає обмежень на JavaScript

### 2. Рекомендовані хостинг провайдери
- **Netlify** (безкоштовний)
- **Vercel** (безкоштовний)
- **GitHub Pages** (безкоштовний)
- **Cloudflare Pages** (безкоштовний)

## Розгортання

### Netlify
1. Зареєструйтеся на [netlify.com](https://netlify.com)
2. Перетягніть папку з файлами на дашборд
3. Скопіюйте URL (наприклад: `https://your-site.netlify.app`)

### Vercel
1. Зареєструйтеся на [vercel.com](https://vercel.com)
2. Підключіть GitHub репозиторій або завантажте файли
3. Скопіюйте URL (наприклад: `https://your-site.vercel.app`)

## Використання скрипта

### URL структура для перенаправлення

```
https://your-domain.com/thanks-gads.html?sub_id_10=AW-***********/A-HRCO7lzeEaEKvm_sU_&link=https://target-url.com&value=100&currency=EUR
```

### Параметри URL

- `sub_id_10` - Повний Google Ads Conversion ID (AW-XXXXXXXXX/CONVERSION_LABEL)
- `link` - URL для перенаправлення після обробки (опційно)
- `value` - Вартість конверсії (опційно, за замовчуванням 1.0, або 'none' для відключення)
- `currency` - Валюта конверсії (опційно, за замовчуванням 'USD')

### Формат Conversion ID

Conversion ID має формат: `AW-ACCOUNT_ID/CONVERSION_LABEL`

**Приклад**: `AW-***********/A-HRCO7lzeEaEKvm_sU_`
- `AW-***********` - Account ID (для gtag config)
- `A-HRCO7lzeEaEKvm_sU_` - Conversion Label
- Повний ID використовується для conversion event

### Приклад використання в Keitaro
1. **Налаштування кампанії**:
   - Створіть кампанію в Keitaro
   - Додайте `sub_id_10` параметр в налаштуваннях

2. **Конфігурація постбека**:
   ```
   https://your-domain.com/thanks-gads.html?sub_id_10={sub_id_10}&link={offer_url}
   ```

## Як працює скрипт

### 1. Отримання та розділення параметрів

```javascript
// Витягує sub_id_10 з URL
var sub_id_10 = getUrlParameter('sub_id_10');

// Розділяє по "/" на aw та conv_id
var parts = sub_id_10.split('/');
var aw = parts[0];        // AW-***********
var conv_id = parts[1];   // A-HRCO7lzeEaEKvm_sU_
```

### 2. Збереження в localStorage

```javascript
// Зберігає повний ID для подальшого використання
localStorage.setItem('sub_id_10', sub_id_10);
```

### 3. Відправка постбека з рандомним піддоменом

```javascript
// Генерує рандомний піддомен
var randomSubdomain = generateRandomSubdomain();
var postbackUrl = 'https://' + randomSubdomain + '.domain.com?=true&sub_id_10=' + encodeURIComponent(subId);
```

### 4. Google Ads трекінг з правильною структурою

```javascript
// Завантажує gtag.js з AW ID
gtag_script.src = 'https://www.googletagmanager.com/gtag/js?id=' + aw;

// Конфігурує з AW ID
gtag('config', aw);

// Отримує вартість та валюту з URL параметрів
var conversionValue = getUrlParameter('value') || 1.0;
var conversionCurrency = getUrlParameter('currency') || 'USD';

// Створює об'єкт конверсії
var conversionData = {
    'send_to': sub_id_10  // Повний ID з лейблом
};

// Додає вартість тільки якщо не 'none'
if (conversionValue !== 'none') {
    conversionData['value'] = parseFloat(conversionValue);
    conversionData['currency'] = conversionCurrency;
}

// Відправляє конверсію
gtag('event', 'conversion', conversionData);
```

## Налаштування в Google Ads

### 1. Отримання Conversion ID

1. Перейдіть в Google Ads → Інструменти → Конверсії
2. Створіть нову конверсію або виберіть існуючу
3. Скопіюйте **повний** Conversion ID (формат: AW-XXXXXXXXX/CONVERSION_LABEL)

**Приклад**: `AW-***********/A-HRCO7lzeEaEKvm_sU_`

### 2. Налаштування в Keitaro

1. Додайте `sub_id_10` в налаштування кампанії
2. Встановіть значення = **повний** Google Ads Conversion ID з лейблом
3. Налаштуйте постбек URL на `thanks-gads.html`

## Налаштування вартості конверсії

### Через URL параметри (рекомендовано)

**Приклади URL:**
```
# Конверсія з вартістю 50 USD
https://your-domain.com/thanks-gads.html?sub_id_10=AW-123456789/Label&value=50&currency=USD

# Конверсія з вартістю 100 EUR
https://your-domain.com/thanks-gads.html?sub_id_10=AW-123456789/Label&value=100&currency=EUR

# Конверсія без вартості
https://your-domain.com/thanks-gads.html?sub_id_10=AW-123456789/Label&value=none

# Конверсія з дефолтними значеннями (1.0 USD)
https://your-domain.com/thanks-gads.html?sub_id_10=AW-123456789/Label
```

### Через код (лінії 227-231)

Для зміни дефолтних значень в коді:

```javascript
// Лінія 227: Змінити дефолтну вартість
var conversionValue = getUrlParameter('value') || 10.0;  // Замість 1.0

// Лінія 228: Змінити дефолтну валюту  
var conversionCurrency = getUrlParameter('currency') || 'EUR';  // Замість 'USD'
```

### Відключення відстеження вартості за замовчуванням

Щоб конверсії за замовчуванням йшли без вартості:

1. **Закоментуйте лінії 227-228:**
```javascript
// var conversionValue = getUrlParameter('value') || 1.0;
// var conversionCurrency = getUrlParameter('currency') || 'USD';
```

2. **Розкоментуйте лінію 231:**
```javascript
var conversionValue = getUrlParameter('value') || 'none';
```

Після цього:
- За замовчуванням конверсії без вартості
- Можна передавати вартість через URL: `?value=50&currency=USD`

### Налаштування в Keitaro

Для динамічної передачі вартості з Keitaro:
```
https://your-domain.com/thanks-gads.html?sub_id_10={sub_id_10}&value={payout}&currency=USD
```

## Тестування

### Перевірка функціональності

1. Відкрийте: `https://your-domain.com/thanks-gads.html?sub_id_10=AW-***********/A-HRCO7lzeEaEKvm_sU_&value=25&currency=EUR`
2. Перевірте консоль браузера на наявність логів
3. Перевірте localStorage: `localStorage.getItem('sub_id_10')`

### Очікувані логи в консолі

```
Stored sub_id_10: AW-***********/A-HRCO7lzeEaEKvm_sU_
Split conversion ID - AW: AW-*********** Conv ID: A-HRCO7lzeEaEKvm_sU_
Postback sent: https://abc123.domain.com?=true&sub_id_10=AW-***********/A-HRCO7lzeEaEKvm_sU_
Google Ads tracking initialized with AW: AW-***********
Conversion sent to: AW-***********/A-HRCO7lzeEaEKvm_sU_
```

## Безпека

### Обмеження
- Використовуйте HTTPS для захисту даних
- Перевіряйте валідність sub_id_10 перед використанням
- Обмежте доступ до адміністративних функцій

### Моніторинг

- Стежте за консоллю браузера на предмет помилок
- Перевіряйте статистику в Google Ads
- Моніторьте трафік на постбек URL з рандомними піддоменами
- Перевіряйте правильність розділення Conversion ID

## Підтримка

### Можливі проблеми
1. **Не працює Google Ads трекінг**
   - Перевірте правильність повного Conversion ID з лейблом
   - Переконайтеся, що сайт використовує HTTPS
   - Перевірте, чи коректно розділяється ID по "/"

2. **Не відправляється постбек**
   - Перевірте доступність цільового URL з рандомним піддоменом
   - Перевірте CORS налаштування
   - Перевірте, чи генерується рандомний піддомен

3. **Не зберігається sub_id_10**
   - Перевірте підтримку localStorage в браузері
   - Перевірте правильність URL параметра з повним ID

### Логи та дебаг
Всі ключові дії логуються в консоль браузера (F12 → Console).

---

**Примітка**: Цей скрипт призначений для легального відстеження конверсій в рекламних кампаніях. Використовуйте відповідально та згідно з політикою конфіденційності.

## Технічні деталі

### Структура коду

- **URL Parameter Parsing**: Функції для роботи з URL параметрами
- **Random Subdomain Generation**: Генерація рандомних піддоменів для постбеків
- **Google Ads Integration**: Динамічне завантаження та конфігурація gtag.js
- **LocalStorage Management**: Збереження та відновлення параметрів
- **Automatic Redirection**: Перенаправлення з збереженням параметрів

### Безпека

- Всі URL параметри проходять `encodeURIComponent()`
- Використовуються рандомні піддомени для розподілу навантаження
- Параметри зберігаються локально в браузері
- Автоматичне перенаправлення після обробки