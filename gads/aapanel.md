# aaPanel Deployment

## Швидкий деплой

### Крок 1: Закачай файл

1. **Files** → перейди в `/www/wwwroot/monegeek.store`
2. **Upload** → загрузи `thanks-gads.html` з папки `gads`
3. **Permissions** → `644`

### Крок 2: Додай домен

1. **Website** → **Add site** → `monegeek.store`
2. **SSL** → **Let's Encrypt** → додай `monegeek.store` і `*.monegeek.store`

### Крок 3: DNS

У свого провайдера домену:
```
A @ твій-сервер-ip
A * твій-сервер-ip
```

## Готово!

- `https://monegeek.store/thanks-gads.html` працює
- `https://random.monegeek.store/thanks-gads.html` працює

### Postback URL для Keitaro:
```
https://monegeek.store/thanks-gads.html?sub_id_10={sub_id_10}
```

Обновив файл? Просто перезалий через Files в aaPanel.