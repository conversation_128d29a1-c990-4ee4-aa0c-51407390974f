<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test: Parameter Parsing</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-case { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .pass { background-color: #d4edda; border-color: #c3e6cb; }
        .fail { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🧪 Test: Parameter Parsing</h1>
    <p>Тестування функції парсингу URL параметрів</p>

    <div id="test-results"></div>

    <button onclick="runAllTests()">🚀 Запустити всі тести</button>
    <button onclick="clearResults()">🗑️ Очистити результати</button>

    <script>
        // Копія функції з основного файлу
        function getUrlParameter(sParam) {
            var sPageURL = window.location.search.substring(1),
                sURLVariables = sPageURL.split('&'),
                sParameterName,
                i;

            for (i = 0; i < sURLVariables.length; i++) {
                sParameterName = sURLVariables[i].split('=');
                if (sParameterName[0] === sParam) {
                    return sParameterName[1] === undefined ? true : decodeURIComponent(sParameterName[1]);
                }
            }
            return false;
        }

        // Тестова функція для симуляції URL
        function testGetUrlParameter(url, param) {
            const originalSearch = window.location.search;
            
            // Тимчасово змінюємо search
            const urlObj = new URL(url, window.location.origin);
            Object.defineProperty(window.location, 'search', {
                writable: true,
                value: urlObj.search
            });
            
            const result = getUrlParameter(param);
            
            // Відновлюємо оригінальний search
            Object.defineProperty(window.location, 'search', {
                writable: true,
                value: originalSearch
            });
            
            return result;
        }

        function addTestResult(testName, passed, expected, actual, details = '') {
            const resultsDiv = document.getElementById('test-results');
            const testDiv = document.createElement('div');
            testDiv.className = `test-case ${passed ? 'pass' : 'fail'}`;
            
            testDiv.innerHTML = `
                <h3>${passed ? '✅' : '❌'} ${testName}</h3>
                <p><strong>Очікувалось:</strong> <code>${expected}</code></p>
                <p><strong>Отримано:</strong> <code>${actual}</code></p>
                ${details ? `<p><strong>Деталі:</strong> ${details}</p>` : ''}
            `;
            
            resultsDiv.appendChild(testDiv);
        }

        function runAllTests() {
            clearResults();
            
            console.log('🧪 Запуск тестів парсингу параметрів...');
            
            // Тест 1: Базовий sub_id_10
            const test1Url = '?sub_id_10=AW-123456789/TestLabel';
            const test1Result = testGetUrlParameter(test1Url, 'sub_id_10');
            const test1Expected = 'AW-123456789/TestLabel';
            addTestResult(
                'Базовий sub_id_10 параметр',
                test1Result === test1Expected,
                test1Expected,
                test1Result,
                'Перевірка парсингу основного параметра конверсії'
            );

            // Тест 2: Параметр value
            const test2Url = '?sub_id_10=AW-123/Label&value=100.50';
            const test2Result = testGetUrlParameter(test2Url, 'value');
            const test2Expected = '100.50';
            addTestResult(
                'Параметр value',
                test2Result === test2Expected,
                test2Expected,
                test2Result,
                'Перевірка парсингу значення конверсії'
            );

            // Тест 3: Параметр currency
            const test3Url = '?currency=EUR&sub_id_10=AW-123/Label';
            const test3Result = testGetUrlParameter(test3Url, 'currency');
            const test3Expected = 'EUR';
            addTestResult(
                'Параметр currency',
                test3Result === test3Expected,
                test3Expected,
                test3Result,
                'Перевірка парсингу валюти'
            );

            // Тест 4: URL encoded параметри
            const test4Url = '?sub_id_10=AW-123456789%2FTestLabel&link=https%3A%2F%2Fexample.com';
            const test4Result = testGetUrlParameter(test4Url, 'sub_id_10');
            const test4Expected = 'AW-123456789/TestLabel';
            addTestResult(
                'URL encoded параметри',
                test4Result === test4Expected,
                test4Expected,
                test4Result,
                'Перевірка декодування URL encoded значень'
            );

            // Тест 5: Відсутній параметр
            const test5Url = '?other_param=value';
            const test5Result = testGetUrlParameter(test5Url, 'sub_id_10');
            const test5Expected = false;
            addTestResult(
                'Відсутній параметр',
                test5Result === test5Expected,
                test5Expected,
                test5Result,
                'Перевірка поведінки при відсутності параметра'
            );

            // Тест 6: Множинні параметри
            const test6Url = '?utm_source=google&sub_id_10=AW-999/Label&utm_medium=cpc&value=50';
            const test6Result = testGetUrlParameter(test6Url, 'sub_id_10');
            const test6Expected = 'AW-999/Label';
            addTestResult(
                'Множинні параметри',
                test6Result === test6Expected,
                test6Expected,
                test6Result,
                'Перевірка парсингу серед багатьох параметрів'
            );

            // Тест 7: Парсинг AW ID та Label
            const test7Url = '?sub_id_10=AW-987654321/ConversionLabel123';
            const sub_id_10 = testGetUrlParameter(test7Url, 'sub_id_10');
            const parts = sub_id_10 ? sub_id_10.split('/') : [];
            const awId = parts[0] || '';
            const label = parts[1] || '';
            
            addTestResult(
                'Розділення AW ID та Label',
                awId === 'AW-987654321' && label === 'ConversionLabel123',
                'AW: AW-987654321, Label: ConversionLabel123',
                `AW: ${awId}, Label: ${label}`,
                'Перевірка правильного розділення sub_id_10 на компоненти'
            );

            console.log('✅ Тести завершено');
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }

        // Автоматичний запуск тестів при завантаженні
        window.onload = function() {
            // Показуємо інформацію про поточні параметри
            const currentParams = new URLSearchParams(window.location.search);
            if (currentParams.toString()) {
                const infoDiv = document.createElement('div');
                infoDiv.className = 'test-case info';
                infoDiv.innerHTML = `
                    <h3>ℹ️ Поточні URL параметри</h3>
                    <pre>${currentParams.toString()}</pre>
                    <p>Ці параметри будуть використані для додаткового тестування</p>
                `;
                document.getElementById('test-results').appendChild(infoDiv);
            }
            
            runAllTests();
        };
    </script>
</body>
</html>
