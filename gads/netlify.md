# Netlify Deployment

## Швидкий деплой

### Крок 1: Деплой на Netlify

1. **New site from Git** → обери свій репозиторій
2. **Build settings:**
   - Build command: `echo 'Static site'`
   - Publish directory: `gads`
3. **Deploy site**

## Готово

Отримаєш URL типу: `https://amazing-name-123456.netlify.app`

### Postback URL для Keitaro

```
https://твій-сайт.netlify.app/thanks-gads.html?sub_id_10={sub_id_10}
```

**Файли `netlify.toml` і `_redirects` вже додані в репозиторій**