# Keitaro Postback Flow для Google Ads

## Налаштування постбеків в Keitaro

### 1. Базове налаштування

- **Основний домен:** `monegeek.store`
- **Робочий файл:** `thanks-gads.html`
- **Постбек URL:** `https://monegeek.store/thanks-gads.html?sub_id_10={sub_id_10}`

### 2. Налаштування в Keitaro

**Базовий URL:**
```
URL: https://monegeek.store/thanks-gads.html?sub_id_10={sub_id_10}
Method: GET
Status: Sale
```

**З динамічною вартістю:**
```
URL: https://monegeek.store/thanks-gads.html?sub_id_10={sub_id_10}&value={payout}&currency=USD
Method: GET
Status: Sale
```

### 3. Структура sub_id_10

Параметр `{sub_id_10}` має формат: `AW-XXXXXXXXX/CONVERSION_LABEL`

**Приклад:**
- `sub_id_10=AW-123456789/AbCdEfGhIjKlMnOp`
- Розділяється на:
  - `aw` частина: `AW-123456789`
  - `conversion_label`: `AbCdEfGhIjKlMnOp`

### 4. Як працює система

1. **Keitaro відправляє постбек** → `https://monegeek.store/thanks-gads.html?sub_id_10=AW-123456789/AbCdEfGhIjKlMnOp`

2. **thanks-gads.html генерує випадковий сабдомен** → `a4b7c9.monegeek.store`

3. **Створюється iframe з Google Ads кодом**, який відображається як відправлений з випадкового сабдомена

4. **Google Ads отримує конверсію** з випадкового сабдомена `a4b7c9.monegeek.store`

### 5. Результат

**Кожна конверсія відправляється в Google Ads з РІЗНОГО випадкового сабдомена:**

- Конверсія 1: `a4b7c9.monegeek.store`
- Конверсія 2: `x8y5z3.monegeek.store`
- Конверсія 3: `m9n4p7.monegeek.store`

### 6. Тестування

**Тестовий URL:**
```
https://monegeek.store/thanks-gads.html?sub_id_10=AW-123456789/AbCdEfGhIjKlMnOp
```

**Очікувані результати в консолі:**
```
Stored sub_id_10: AW-123456789/AbCdEfGhIjKlMnOp
Split conversion ID - AW: AW-123456789 Conv ID: AbCdEfGhIjKlMnOp
Google Ads conversion iframe created with subdomain: a4b7c9.monegeek.store
Google Ads conversion sent from subdomain: a4b7c9.monegeek.store
```

### 7. Важливо

- **Потрібен тільки ОДИН файл:** `thanks-gads.html`
- **Не потрібно налаштовувати DNS для сабдоменів** - все працює через iframe
- **Кожна конверсія автоматично відправляється з різного сабдомена**