<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test: Conversion Sending</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-case { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .pass { background-color: #d4edda; border-color: #c3e6cb; }
        .fail { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        .iframe-container { border: 1px solid #ccc; height: 200px; overflow: auto; }
        .log-entry { padding: 5px; border-bottom: 1px solid #eee; font-family: monospace; font-size: 12px; }
        .log-success { background-color: #d4edda; }
        .log-error { background-color: #f8d7da; }
        .log-info { background-color: #d1ecf1; }
    </style>
</head>
<body>
    <h1>🧪 Test: Conversion Sending</h1>
    <p>Тестування відправки конверсій через iframe на субдомени</p>

    <div id="test-results"></div>

    <button onclick="runBasicTests()">🚀 Базові тести</button>
    <button onclick="testRealConversion()">🎯 Тест реальної конверсії</button>
    <button onclick="testMultipleConversions()">🔄 Множинні конверсії</button>
    <button onclick="clearResults()">🗑️ Очистити результати</button>

    <script>
        let testLog = [];

        // Копії функцій з основних файлів
        function generateRandomSubdomain() {
            return (Math.random() + 1).toString(36).substring(2, 8);
        }

        function getBaseDomain() {
            var hostname = window.location.hostname;
            var parts = hostname.split('.');
            if (parts.length > 2) {
                return parts.slice(-2).join('.');
            }
            return hostname;
        }

        function getUrlParameter(sParam) {
            var sPageURL = window.location.search.substring(1),
                sURLVariables = sPageURL.split('&'),
                sParameterName,
                i;

            for (i = 0; i < sURLVariables.length; i++) {
                sParameterName = sURLVariables[i].split('=');
                if (sParameterName[0] === sParam) {
                    return sParameterName[1] === undefined ? true : decodeURIComponent(sParameterName[1]);
                }
            }
            return false;
        }

        function logMessage(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            testLog.push({timestamp, message, type});
            console.log(`[${timestamp}] ${message}`);
        }

        function addTestResult(testName, passed, details = '', extraContent = '') {
            const resultsDiv = document.getElementById('test-results');
            const testDiv = document.createElement('div');
            testDiv.className = `test-case ${passed ? 'pass' : 'fail'}`;
            
            testDiv.innerHTML = `
                <h3>${passed ? '✅' : '❌'} ${testName}</h3>
                <p>${details}</p>
                ${extraContent}
            `;
            
            resultsDiv.appendChild(testDiv);
        }

        function addInfoResult(title, content) {
            const resultsDiv = document.getElementById('test-results');
            const testDiv = document.createElement('div');
            testDiv.className = 'test-case info';
            testDiv.innerHTML = `<h3>ℹ️ ${title}</h3>${content}`;
            resultsDiv.appendChild(testDiv);
        }

        function addWarningResult(title, content) {
            const resultsDiv = document.getElementById('test-results');
            const testDiv = document.createElement('div');
            testDiv.className = 'test-case warning';
            testDiv.innerHTML = `<h3>⚠️ ${title}</h3>${content}`;
            resultsDiv.appendChild(testDiv);
        }

        function createTestIframe(subdomain, params) {
            return new Promise((resolve, reject) => {
                const iframe = document.createElement('iframe');
                iframe.style.display = 'none';
                iframe.style.width = '1px';
                iframe.style.height = '1px';
                
                const baseDomain = getBaseDomain();
                const targetDomain = subdomain + '.' + baseDomain;
                
                const url = `https://${targetDomain}/gads/conversion-sender.html?${params}`;
                
                logMessage(`Створення iframe: ${url}`, 'info');
                
                iframe.onload = function() {
                    logMessage(`Iframe завантажено: ${targetDomain}`, 'success');
                    resolve({iframe, url, domain: targetDomain});
                };
                
                iframe.onerror = function() {
                    logMessage(`Помилка завантаження iframe: ${targetDomain}`, 'error');
                    reject(new Error(`Failed to load iframe: ${targetDomain}`));
                };
                
                iframe.src = url;
                document.body.appendChild(iframe);
                
                // Таймаут для завантаження
                setTimeout(() => {
                    if (!iframe.contentDocument || iframe.contentDocument.readyState !== 'complete') {
                        logMessage(`Таймаут завантаження iframe: ${targetDomain}`, 'error');
                        reject(new Error(`Timeout loading iframe: ${targetDomain}`));
                    }
                }, 5000);
            });
        }

        function runBasicTests() {
            clearResults();
            testLog = [];
            
            logMessage('Запуск базових тестів конверсій', 'info');

            // Тест 1: Генерація параметрів
            const awId = 'AW-123456789';
            const label = 'TestLabel';
            const value = '100.50';
            const currency = 'USD';
            
            const params = `aw=${encodeURIComponent(awId)}&label=${encodeURIComponent(label)}&value=${encodeURIComponent(value)}&currency=${encodeURIComponent(currency)}`;
            
            addTestResult(
                'Генерація параметрів конверсії',
                params.includes('aw=') && params.includes('label='),
                `Параметри: <code>${params}</code>`
            );

            // Тест 2: Генерація субдомена
            const subdomain = generateRandomSubdomain();
            const baseDomain = getBaseDomain();
            const fullDomain = subdomain + '.' + baseDomain;
            
            addTestResult(
                'Генерація субдомена',
                subdomain.length === 6 && /^[a-z0-9]+$/.test(subdomain),
                `Субдомен: <code>${subdomain}</code><br>Повний домен: <code>${fullDomain}</code>`
            );

            // Тест 3: Створення URL
            const conversionUrl = `https://${fullDomain}/gads/conversion-sender.html?${params}`;
            const validUrl = conversionUrl.startsWith('https://') && conversionUrl.includes('conversion-sender.html');
            
            addTestResult(
                'Створення URL конверсії',
                validUrl,
                `URL: <code>${conversionUrl}</code>`
            );

            // Показуємо лог
            displayTestLog();
        }

        function testRealConversion() {
            testLog = [];
            logMessage('Тестування реальної конверсії', 'info');

            const subdomain = generateRandomSubdomain();
            const params = 'aw=AW-123456789&label=TestLabel&value=100&currency=USD';
            
            addWarningResult(
                'Тест реальної конверсії',
                `
                <p>⚠️ Цей тест спробує створити реальний iframe для відправки конверсії.</p>
                <p><strong>Субдомен:</strong> <code>${subdomain}.${getBaseDomain()}</code></p>
                <p><strong>Параметри:</strong> <code>${params}</code></p>
                <p>Перевірте консоль браузера та Network tab для деталей.</p>
                <div id="real-conversion-status">Створення iframe...</div>
                `
            );

            createTestIframe(subdomain, params)
                .then(result => {
                    logMessage(`Успішно створено iframe: ${result.domain}`, 'success');
                    document.getElementById('real-conversion-status').innerHTML = 
                        `✅ Iframe створено успішно<br>Домен: <code>${result.domain}</code>`;
                })
                .catch(error => {
                    logMessage(`Помилка створення iframe: ${error.message}`, 'error');
                    document.getElementById('real-conversion-status').innerHTML = 
                        `❌ Помилка: ${error.message}`;
                });

            setTimeout(() => {
                displayTestLog();
            }, 6000);
        }

        function testMultipleConversions() {
            testLog = [];
            logMessage('Тестування множинних конверсій', 'info');

            const conversions = [
                {aw: 'AW-111111111', label: 'Label1', value: '50', currency: 'USD'},
                {aw: 'AW-222222222', label: 'Label2', value: '75', currency: 'EUR'},
                {aw: 'AW-333333333', label: 'Label3', value: '100', currency: 'GBP'}
            ];

            addInfoResult(
                'Множинні конверсії',
                `
                <p>Тестування ${conversions.length} конверсій з різних субдоменів</p>
                <div id="multiple-status">Запуск...</div>
                `
            );

            let completed = 0;
            const results = [];

            conversions.forEach((conv, index) => {
                const subdomain = generateRandomSubdomain();
                const params = `aw=${conv.aw}&label=${conv.label}&value=${conv.value}&currency=${conv.currency}`;
                
                setTimeout(() => {
                    createTestIframe(subdomain, params)
                        .then(result => {
                            completed++;
                            results.push({success: true, domain: result.domain, conv});
                            updateMultipleStatus(completed, conversions.length, results);
                        })
                        .catch(error => {
                            completed++;
                            results.push({success: false, error: error.message, conv});
                            updateMultipleStatus(completed, conversions.length, results);
                        });
                }, index * 1000); // Затримка між конверсіями
            });
        }

        function updateMultipleStatus(completed, total, results) {
            const statusDiv = document.getElementById('multiple-status');
            if (statusDiv) {
                const successful = results.filter(r => r.success).length;
                const failed = results.filter(r => !r.success).length;
                
                statusDiv.innerHTML = `
                    Завершено: ${completed}/${total}<br>
                    Успішних: ${successful}<br>
                    Невдалих: ${failed}
                `;

                if (completed === total) {
                    statusDiv.innerHTML += '<br>✅ Всі тести завершено';
                    displayTestLog();
                }
            }
        }

        function displayTestLog() {
            const logContent = testLog.map(entry => 
                `<div class="log-entry log-${entry.type}">[${entry.timestamp}] ${entry.message}</div>`
            ).join('');

            addInfoResult(
                'Лог виконання',
                `<div class="iframe-container">${logContent}</div>`
            );
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
            testLog = [];
        }

        // Слухач повідомлень від iframe
        window.addEventListener('message', function(event) {
            if (event.data && event.data.type === 'conversion_sent') {
                logMessage(`Отримано підтвердження конверсії з ${event.data.domain}`, 'success');
            }
        });

        // Автоматичний запуск при завантаженні
        window.onload = function() {
            addInfoResult(
                'Інформація про тестування',
                `
                <p>Цей тест перевіряє функціональність відправки конверсій через iframe.</p>
                <p><strong>Поточний домен:</strong> <code>${window.location.hostname}</code></p>
                <p><strong>Базовий домен:</strong> <code>${getBaseDomain()}</code></p>
                <p>⚠️ Для повного тестування потрібно щоб файл <code>conversion-sender.html</code> був доступний на всіх субдоменах.</p>
                `
            );
        };
    </script>
</body>
</html>
