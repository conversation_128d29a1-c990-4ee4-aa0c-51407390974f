<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Conversion Tracker</title>
</head>
<body>
    <h1>Processing conversion...</h1>

<script>
    /**
     * Get URL parameter value by name
     */
    function getUrlParameter(sParam) {
        var sPageURL = window.location.search.substring(1),
            sURLVariables = sPageURL.split('&'),
            sParameterName,
            i;

        for (i = 0; i < sURLVariables.length; i++) {
            sParameterName = sURLVariables[i].split('=');
            if (sParameterName[0] === sParam) {
                return sParameterName[1] === undefined ? true : decodeURIComponent(sParameterName[1]);
            }
        }
        return false;
    }

    /**
     * Generate random subdomain string
     */
    function generateRandomSubdomain() {
        return (Math.random() + 1).toString(36).substring(2, 8);
    }

    /**
     * Get base domain (remove subdomain)
     */
    function getBaseDomain() {
        var hostname = window.location.hostname;
        var parts = hostname.split('.');
        if (parts.length > 2) {
            return parts.slice(-2).join('.');
        }
        return hostname;
    }

    /**
     * Send conversion from random subdomain
     */
    function sendConversionFromSubdomain(awId, conversionLabel, value, currency) {
        var randomSubdomain = generateRandomSubdomain();
        var baseDomain = getBaseDomain();
        var targetDomain = randomSubdomain + '.' + baseDomain;

        console.log('🎯 Sending conversion from subdomain:', targetDomain);
        console.log('   AW ID:', awId);
        console.log('   Conversion Label:', conversionLabel);
        console.log('   Value:', value, currency);

        // Create iframe pointing to subdomain
        var iframe = document.createElement('iframe');
        iframe.style.display = 'none';
        iframe.style.width = '1px';
        iframe.style.height = '1px';

        // Build conversion URL for subdomain
        var conversionUrl = 'https://' + targetDomain + '/conversion-sender.html' +
            '?aw=' + encodeURIComponent(awId) +
            '&label=' + encodeURIComponent(conversionLabel) +
            '&value=' + encodeURIComponent(value) +
            '&currency=' + encodeURIComponent(currency);

        iframe.src = conversionUrl;
        document.body.appendChild(iframe);

        console.log('📡 Iframe created with URL:', conversionUrl);

        return targetDomain;
    }

    // Main execution
    var sub_id_10 = getUrlParameter('sub_id_10');
    var awId = '';
    var conversionLabel = '';
    
    if (sub_id_10) {
        console.log('📥 Received sub_id_10:', sub_id_10);
        
        // Parse AW-XXXXXXXXX/CONVERSION_LABEL
        var parts = sub_id_10.split('/');
        if (parts.length >= 2) {
            awId = parts[0];
            conversionLabel = parts[1];
            
            var value = getUrlParameter('value') || '1.0';
            var currency = getUrlParameter('currency') || 'USD';
            
            // Send conversion from random subdomain
            var usedDomain = sendConversionFromSubdomain(awId, conversionLabel, value, currency);
            
            console.log('✅ Conversion dispatched from:', usedDomain);
            
        } else {
            console.log('❌ Invalid sub_id_10 format. Expected: AW-XXXXXXXXX/CONVERSION_LABEL');
        }
    } else {
        console.log('❌ Missing sub_id_10 parameter');
    }

    // Redirect after delay
    setTimeout(function() {
        var redirectUrl = getUrlParameter('link') || 'https://www.google.com/';
        console.log('🔄 Redirecting to:', redirectUrl);
        window.location.href = redirectUrl;
    }, 2000);

</script>
</body>
</html>
