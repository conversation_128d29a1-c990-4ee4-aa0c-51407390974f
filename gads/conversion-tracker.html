<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Conversion Tracker</title>
</head>
<body>
    <h1>Processing conversion...</h1>

<script>
    /**
     * Get URL parameter value by name
     */
    function getUrlParameter(sParam) {
        var sPageURL = window.location.search.substring(1),
            sURLVariables = sPageURL.split('&'),
            sParameterName,
            i;

        for (i = 0; i < sURLVariables.length; i++) {
            sParameterName = sURLVariables[i].split('=');
            if (sParameterName[0] === sParam) {
                return sParameterName[1] === undefined ? true : decodeURIComponent(sParameterName[1]);
            }
        }
        return false;
    }

    /**
     * Generate random subdomain string
     */
    function generateRandomSubdomain() {
        return (Math.random() + 1).toString(36).substring(2, 8);
    }

    /**
     * Get base domain (remove subdomain)
     */
    function getBaseDomain() {
        var hostname = window.location.hostname;
        var parts = hostname.split('.');
        if (parts.length > 2) {
            return parts.slice(-2).join('.');
        }
        return hostname;
    }

    /**
     * Send conversion directly from main domain
     */
    function sendConversion(awId, conversionLabel, value, currency) {
        console.log('🎯 Sending Google Ads conversion');
        console.log('   AW ID:', awId);
        console.log('   Conversion Label:', conversionLabel);
        console.log('   Value:', value, currency);

        // Load Google Ads gtag script
        var gtagScript = document.createElement('script');
        gtagScript.async = true;
        gtagScript.src = 'https://www.googletagmanager.com/gtag/js?id=' + awId;
        document.head.appendChild(gtagScript);

        console.log('📡 Loading gtag script for:', awId);

        gtagScript.onload = function() {
            console.log('✅ Gtag script loaded successfully');

            // Initialize gtag
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', awId);

            console.log('⚙️ Gtag configured for AW ID:', awId);

            // Send conversion
            var conversionData = {
                'send_to': awId + '/' + conversionLabel
            };

            // Add value if not 'none'
            if (value !== 'none') {
                conversionData['value'] = parseFloat(value);
                conversionData['currency'] = currency;
            }

            gtag('event', 'conversion', conversionData);

            console.log('🎯 CONVERSION SENT TO GOOGLE ADS!');
            console.log('   From domain:', window.location.hostname);
            console.log('   To:', awId + '/' + conversionLabel);
            if (value !== 'none') {
                console.log('   Value:', value + ' ' + currency);
            }
            console.log('   Data:', conversionData);
        };

        gtagScript.onerror = function() {
            console.log('❌ Failed to load gtag script');
        };

        return window.location.hostname;
    }

    // Main execution
    var sub_id_10 = getUrlParameter('sub_id_10');
    var awId = '';
    var conversionLabel = '';
    
    if (sub_id_10) {
        console.log('📥 Received sub_id_10:', sub_id_10);
        
        // Parse AW-XXXXXXXXX/CONVERSION_LABEL
        var parts = sub_id_10.split('/');
        if (parts.length >= 2) {
            awId = parts[0];
            conversionLabel = parts[1];
            
            var value = getUrlParameter('value') || '1.0';
            var currency = getUrlParameter('currency') || 'USD';
            
            // Send conversion directly
            var usedDomain = sendConversion(awId, conversionLabel, value, currency);
            
            console.log('✅ Conversion dispatched from:', usedDomain);
            
        } else {
            console.log('❌ Invalid sub_id_10 format. Expected: AW-XXXXXXXXX/CONVERSION_LABEL');
        }
    } else {
        console.log('❌ Missing sub_id_10 parameter');
    }

    // Redirect after delay
    setTimeout(function() {
        var redirectUrl = getUrlParameter('link') || 'https://www.google.com/';
        console.log('🔄 Redirecting to:', redirectUrl);
        window.location.href = redirectUrl;
    }, 2000);

</script>
</body>
</html>
