# Google Ads Multi-Subdomain Conversion Tracking

## Огляд системи

Система дозволяє відправляти Google Ads конверсії з різних випадкових субдоменів для кожної конверсії, використовуючи Cloudflare wildcard домени.

## Архітектура

```
Користувач → conversion-tracker.html → Генерує субдомен → iframe → conversion-sender.html → Google Ads
```

### Компоненти:

1. **conversion-tracker.html** - Головний файл, приймає параметри і створює iframe
2. **conversion-sender.html** - Виконується на субдомені, відправляє конверсію

## Налаштування Cloudflare

### 1. Wildcard DNS запис
```
Type: A
Name: *
Content: YOUR_SERVER_IP
Proxy: ✅ Proxied
```

### 2. SSL/TLS налаштування
- SSL/TLS encryption mode: **Full** або **Full (strict)**
- Universal SSL: **Enabled**
- Always Use HTTPS: **Enabled**

### 3. Page Rules (опціонально)
```
URL: *.yourdomain.com/*
Settings:
- Cache Level: Bypass
- Security Level: Medium
```

## Структура файлів

```
gads/
├── conversion-tracker.html    # Головний трекер
├── conversion-sender.html     # Відправник конверсій
├── thanks-gads.html          # Оригінальний файл (не змінювати)
└── README.md                 # Ця документація
```

## Параметри URL

### conversion-tracker.html
| Параметр | Обов'язковий | Опис | Приклад |
|----------|--------------|------|---------|
| `sub_id_10` | ✅ | Google Ads ID у форматі AW-XXXXX/LABEL | `AW-123456789/AbCdEfGhIj` |
| `value` | ❌ | Вартість конверсії | `100.50` |
| `currency` | ❌ | Валюта (за замовчуванням USD) | `EUR` |
| `link` | ❌ | URL для редиректу після конверсії | `https://example.com` |

### Приклад використання:
```
https://yourdomain.com/gads/conversion-tracker.html?sub_id_10=AW-123456789/AbCdEfGhIj&value=100&currency=USD&link=https://google.com
```

## Логіка роботи

### Крок 1: Ініціалізація
1. Користувач переходить на `conversion-tracker.html`
2. Скрипт парсить параметр `sub_id_10`
3. Розділяє на `AW ID` та `Conversion Label`

### Крок 2: Генерація субдомена
```javascript
function generateRandomSubdomain() {
    return (Math.random() + 1).toString(36).substring(2, 8);
}
// Результат: abc123, def456, xyz789, etc.
```

### Крок 3: Створення iframe
1. Генерується випадковий субдомен
2. Створюється iframe з URL: `https://abc123.yourdomain.com/gads/conversion-sender.html`
3. Передаються параметри конверсії

### Крок 4: Відправка конверсії
1. `conversion-sender.html` завантажується на субдомені
2. Завантажується Google gtag script
3. Ініціалізується gtag з AW ID
4. Відправляється conversion event

### Крок 5: Редирект
Через 2 секунди відбувається редирект на вказаний URL або Google.com

## Схема потоку даних

```mermaid
graph TD
    A[Користувач клікає посилання] --> B[conversion-tracker.html]
    B --> C[Парсинг sub_id_10]
    C --> D[Генерація випадкового субдомена]
    D --> E[Створення iframe]
    E --> F[abc123.domain.com/conversion-sender.html]
    F --> G[Завантаження gtag]
    G --> H[Відправка конверсії в Google Ads]
    H --> I[Редирект користувача]
```

## Переваги системи

### ✅ Множинні субдомени
- Кожна конверсія відправляється з унікального субдомена
- Ускладнює відстеження паттернів

### ✅ Cloudflare інтеграція
- Wildcard SSL сертифікати
- Автоматичне проксування всіх субдоменів
- CDN кешування

### ✅ Гнучкість
- Підтримка динамічних значень конверсій
- Налаштовувані валюти
- Кастомні редиректи

## Моніторинг і дебаг

### Console логи
Система виводить детальні логи в консоль браузера:

```javascript
// В conversion-tracker.html
🎯 Sending conversion from subdomain: abc123.yourdomain.com
📡 Iframe created with URL: https://abc123.yourdomain.com/...

// В conversion-sender.html  
🚀 CONVERSION SENDER LOADED
✅ Gtag script loaded successfully
🎯 CONVERSION SENT!
```

### Перевірка роботи
1. Відкрийте Developer Tools (F12)
2. Перейдіть на вкладку Console
3. Завантажте сторінку з параметрами
4. Перевірте логи відправки

## Troubleshooting

### Проблема: Конверсії не відправляються
**Рішення:**
- Перевірте Cloudflare DNS налаштування
- Переконайтеся що wildcard запис активний
- Перевірте SSL сертифікати

### Проблема: CORS помилки
**Рішення:**
- Переконайтеся що обидва файли доступні на всіх субдоменах
- Перевірте Cloudflare Page Rules

### Проблема: Неправильний формат sub_id_10
**Рішення:**
- Формат повинен бути: `AW-XXXXXXXXX/CONVERSION_LABEL`
- Перевірте що немає зайвих символів

## Безпека

### Рекомендації:
- Використовуйте HTTPS для всіх запитів
- Налаштуйте Content Security Policy
- Обмежте доступ до файлів через .htaccess якщо потрібно

### Приклад .htaccess:
```apache
# Дозволити доступ тільки до HTML файлів
<Files "*.html">
    Order allow,deny
    Allow from all
</Files>
```

## Масштабування

Система автоматично масштабується через Cloudflare:
- Необмежена кількість субдоменів
- Глобальний CDN
- Автоматичне SSL для всіх субдоменів
- DDoS захист

---

**Версія:** 1.0  
**Останнє оновлення:** 2025-07-12
