# Google Ads Multi-Subdomain Conversion Tracking

## Огляд системи

Система дозволяє відправляти Google Ads конверсії з різних випадкових субдоменів для кожної конверсії, використовуючи Cloudflare wildcard домени.

## Архітектура

```
Користувач → conversion-tracker.html → Завантажує gtag → Відправляє конверсію → Google Ads
```

### Компоненти:

1. **conversion-tracker.html** - Головний файл, приймає параметри і відправляє конверсії
2. **conversion-sender.html** - Резервний файл (не використовується в поточній версії)

## Налаштування Cloudflare

### 1. Wildcard DNS запис
```
Type: A
Name: *
Content: YOUR_SERVER_IP
Proxy: ✅ Proxied
```

### 2. SSL/TLS налаштування
- SSL/TLS encryption mode: **Full** або **Full (strict)**
- Universal SSL: **Enabled**
- Always Use HTTPS: **Enabled**

### 3. Page Rules (опціонально)
```
URL: *.yourdomain.com/*
Settings:
- Cache Level: Bypass
- Security Level: Medium
```

## Структура файлів

```
gads/
├── conversion-tracker.html    # Головний трекер
├── conversion-sender.html     # Відправник конверсій
├── thanks-gads.html          # Оригінальний файл (не змінювати)
└── README.md                 # Ця документація
```

## Параметри URL

### conversion-tracker.html
| Параметр | Обов'язковий | Опис | Приклад |
|----------|--------------|------|---------|
| `sub_id_10` | ✅ | Google Ads ID у форматі AW-XXXXX/LABEL | `AW-123456789/AbCdEfGhIj` |
| `value` | ❌ | Вартість конверсії | `100.50` |
| `currency` | ❌ | Валюта (за замовчуванням USD) | `EUR` |
| `link` | ❌ | URL для редиректу після конверсії | `https://example.com` |

### Приклад використання:
```
https://yourdomain.com/gads/conversion-tracker.html?sub_id_10=AW-123456789/AbCdEfGhIj&value=100&currency=USD&link=https://google.com
```

## Логіка роботи

### Крок 1: Ініціалізація
1. Користувач переходить на `conversion-tracker.html`
2. Скрипт парсить параметр `sub_id_10`
3. Розділяє на `AW ID` та `Conversion Label`

### Крок 2: Генерація субдомена
```javascript
function generateRandomSubdomain() {
    return (Math.random() + 1).toString(36).substring(2, 8);
}
// Результат: abc123, def456, xyz789, etc.
```

### Крок 3: Створення iframe
1. Генерується випадковий субдомен
2. Створюється iframe з URL: `https://abc123.yourdomain.com/gads/conversion-sender.html`
3. Передаються параметри конверсії

### Крок 4: Відправка конверсії
1. `conversion-sender.html` завантажується на субдомені
2. Завантажується Google gtag script
3. Ініціалізується gtag з AW ID
4. Відправляється conversion event

### Крок 5: Редирект
Через 2 секунди відбувається редирект на вказаний URL або Google.com

## Схеми архітектури

### Загальна архітектура системи

```mermaid
graph TB
    subgraph "User Journey"
        U[👤 User clicks ad] --> L[🔗 Landing page with tracking URL]
        L --> CT[📄 conversion-tracker.html]
    end

    subgraph "Main Domain Processing"
        CT --> P[🔍 Parse sub_id_10 parameter]
        P --> S[🎲 Generate random subdomain]
        S --> I[🖼️ Create iframe]
    end

    subgraph "Cloudflare Wildcard Domain"
        I --> SD1[🌐 abc123.domain.com]
        I --> SD2[🌐 def456.domain.com]
        I --> SD3[🌐 xyz789.domain.com]
        SD1 --> CS1[📄 conversion-sender.html]
        SD2 --> CS2[📄 conversion-sender.html]
        SD3 --> CS3[📄 conversion-sender.html]
    end

    subgraph "Google Ads Integration"
        CS1 --> GT1[📡 Load gtag script]
        CS2 --> GT2[📡 Load gtag script]
        CS3 --> GT3[📡 Load gtag script]
        GT1 --> GA1[🎯 Send conversion to Google Ads]
        GT2 --> GA2[🎯 Send conversion to Google Ads]
        GT3 --> GA3[🎯 Send conversion to Google Ads]
    end

    subgraph "Final Step"
        GA1 --> R[🔄 Redirect user]
        GA2 --> R
        GA3 --> R
        R --> F[🏁 Final destination]
    end

    style U fill:#e1f5fe
    style CT fill:#f3e5f5
    style SD1 fill:#e8f5e8
    style SD2 fill:#e8f5e8
    style SD3 fill:#e8f5e8
    style GA1 fill:#fff3e0
    style GA2 fill:#fff3e0
    style GA3 fill:#fff3e0
```

### Технічний потік виконання

```mermaid
sequenceDiagram
    participant U as 👤 User
    participant CT as 📄 conversion-tracker.html
    participant CF as ☁️ Cloudflare
    participant SD as 🌐 Random Subdomain
    participant CS as 📄 conversion-sender.html
    participant GA as 🎯 Google Ads

    U->>CT: GET /conversion-tracker.html?sub_id_10=AW-123/LABEL
    CT->>CT: Parse sub_id_10 parameter
    CT->>CT: Generate random subdomain (abc123)
    CT->>CF: Request abc123.domain.com
    CF->>SD: Route to server with wildcard SSL
    CT->>SD: Create iframe to conversion-sender.html
    SD->>CS: Load conversion-sender.html
    CS->>CS: Extract AW ID and Label from URL
    CS->>GA: Load gtag script (gtag/js?id=AW-123)
    GA->>CS: Return gtag library
    CS->>CS: Initialize gtag with AW-123
    CS->>GA: gtag('event', 'conversion', {send_to: 'AW-123/LABEL'})
    GA->>GA: Process conversion from abc123.domain.com
    CS->>CT: postMessage('conversion_sent')
    CT->>U: Redirect to final URL after 2s delay
```

## Переваги системи

### ✅ Множинні субдомени
- Кожна конверсія відправляється з унікального субдомена
- Ускладнює відстеження паттернів

### ✅ Cloudflare інтеграція
- Wildcard SSL сертифікати
- Автоматичне проксування всіх субдоменів
- CDN кешування

### ✅ Гнучкість
- Підтримка динамічних значень конверсій
- Налаштовувані валюти
- Кастомні редиректи

## Моніторинг і дебаг

### Console логи
Система виводить детальні логи в консоль браузера:

```javascript
// В conversion-tracker.html
🎯 Sending conversion from subdomain: abc123.yourdomain.com
📡 Iframe created with URL: https://abc123.yourdomain.com/...

// В conversion-sender.html  
🚀 CONVERSION SENDER LOADED
✅ Gtag script loaded successfully
🎯 CONVERSION SENT!
```

### Перевірка роботи
1. Відкрийте Developer Tools (F12)
2. Перейдіть на вкладку Console
3. Завантажте сторінку з параметрами
4. Перевірте логи відправки

## Troubleshooting

### Проблема: Конверсії не відправляються
**Рішення:**
- Перевірте Cloudflare DNS налаштування
- Переконайтеся що wildcard запис активний
- Перевірте SSL сертифікати

### Проблема: CORS помилки
**Рішення:**
- Переконайтеся що обидва файли доступні на всіх субдоменах
- Перевірте Cloudflare Page Rules

### Проблема: Неправильний формат sub_id_10
**Рішення:**
- Формат повинен бути: `AW-XXXXXXXXX/CONVERSION_LABEL`
- Перевірте що немає зайвих символів

## Безпека

### Рекомендації:
- Використовуйте HTTPS для всіх запитів
- Налаштуйте Content Security Policy
- Обмежте доступ до файлів через .htaccess якщо потрібно

### Приклад .htaccess:
```apache
# Дозволити доступ тільки до HTML файлів
<Files "*.html">
    Order allow,deny
    Allow from all
</Files>
```

## Масштабування

Система автоматично масштабується через Cloudflare:
- Необмежена кількість субдоменів
- Глобальний CDN
- Автоматичне SSL для всіх субдоменів
- DDoS захист

## Тестування системи

### Мануальне тестування

#### Крок 1: Базова перевірка
```
https://yourdomain.com/gads/conversion-tracker.html?sub_id_10=AW-123456789/TestLabel&value=100&currency=USD
```

**Очікуваний результат:**
- Відкриється сторінка "Processing conversion..."
- В консолі з'являться логи з деталями конверсії
- Через 2 секунди відбудеться редирект

#### Крок 2: Перевірка субдоменів
1. Відкрийте Developer Tools (F12)
2. Перейдіть на вкладку Network
3. Завантажте тест URL
4. Перевірте що створюються запити до різних субдоменів

#### Крок 3: Перевірка Google Ads
1. Відкрийте Google Ads аккаунт
2. Перейдіть в Conversions → Summary
3. Перевірте що конверсії надходять з різних доменів

### Інструменти для тестування

#### Browser Console
```javascript
// Перевірка генерації субдоменів
for(let i = 0; i < 5; i++) {
    console.log((Math.random() + 1).toString(36).substring(2, 8));
}

// Перевірка парсингу параметрів
const url = new URL(window.location);
console.log(url.searchParams.get('sub_id_10'));
```

#### Network Monitor
1. F12 → Network tab
2. Фільтр: XHR/Fetch
3. Перевірка запитів до Google Ads

#### Cloudflare Analytics
1. Cloudflare Dashboard → Analytics
2. Перевірка трафіку на субдомени
3. SSL/TLS статус

## 🚀 Швидкий старт

### Приклад URL для трекера:
```
https://yourdomain.com/conversion-tracker.html?sub_id_10=AW-123456789/ConversionLabel&value=100&currency=USD&link=https://google.com
```

### Параметри:
- `sub_id_10` - Google Ads ID (формат: AW-XXXXXXXXX/LABEL)
- `value` - Вартість конверсії (опціонально)
- `currency` - Валюта (опціонально, за замовчуванням USD)
- `link` - URL для редиректу (опціонально)

## Деплой на Coolify

Детальна інструкція: [coolify.md](coolify.md)

---

**Версія:** 1.0
**Останнє оновлення:** 2025-07-12
