<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test: Subdomain Generation</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-case { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .pass { background-color: #d4edda; border-color: #c3e6cb; }
        .fail { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        .subdomain-list { max-height: 200px; overflow-y: auto; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; }
        .stat-box { padding: 10px; background: #f8f9fa; border-radius: 5px; text-align: center; }
    </style>
</head>
<body>
    <h1>🧪 Test: Subdomain Generation</h1>
    <p>Тестування функції генерації випадкових субдоменів</p>

    <div id="test-results"></div>

    <button onclick="runAllTests()">🚀 Запустити всі тести</button>
    <button onclick="generateBatch()">🎲 Згенерувати 100 субдоменів</button>
    <button onclick="clearResults()">🗑️ Очистити результати</button>

    <script>
        // Копія функції з основного файлу
        function generateRandomSubdomain() {
            return (Math.random() + 1).toString(36).substring(2, 8);
        }

        function getBaseDomain() {
            var hostname = window.location.hostname;
            var parts = hostname.split('.');
            if (parts.length > 2) {
                return parts.slice(-2).join('.');
            }
            return hostname;
        }

        function addTestResult(testName, passed, details = '', extraContent = '') {
            const resultsDiv = document.getElementById('test-results');
            const testDiv = document.createElement('div');
            testDiv.className = `test-case ${passed ? 'pass' : 'fail'}`;
            
            testDiv.innerHTML = `
                <h3>${passed ? '✅' : '❌'} ${testName}</h3>
                <p>${details}</p>
                ${extraContent}
            `;
            
            resultsDiv.appendChild(testDiv);
        }

        function addInfoResult(title, content) {
            const resultsDiv = document.getElementById('test-results');
            const testDiv = document.createElement('div');
            testDiv.className = 'test-case info';
            testDiv.innerHTML = `<h3>ℹ️ ${title}</h3>${content}`;
            resultsDiv.appendChild(testDiv);
        }

        function runAllTests() {
            clearResults();
            
            console.log('🧪 Запуск тестів генерації субдоменів...');

            // Тест 1: Базова генерація
            const subdomain1 = generateRandomSubdomain();
            addTestResult(
                'Базова генерація субдомена',
                typeof subdomain1 === 'string' && subdomain1.length === 6,
                `Згенеровано: <code>${subdomain1}</code><br>Довжина: ${subdomain1.length} символів`
            );

            // Тест 2: Унікальність
            const subdomains = [];
            for (let i = 0; i < 100; i++) {
                subdomains.push(generateRandomSubdomain());
            }
            const uniqueSubdomains = [...new Set(subdomains)];
            const uniquenessRate = (uniqueSubdomains.length / subdomains.length) * 100;
            
            addTestResult(
                'Тест унікальності (100 генерацій)',
                uniquenessRate > 95,
                `Унікальних: ${uniqueSubdomains.length} з ${subdomains.length}<br>Унікальність: ${uniquenessRate.toFixed(1)}%`
            );

            // Тест 3: Формат символів
            const testSubdomain = generateRandomSubdomain();
            const validFormat = /^[a-z0-9]{6}$/.test(testSubdomain);
            addTestResult(
                'Формат символів (a-z, 0-9)',
                validFormat,
                `Тестовий субдомен: <code>${testSubdomain}</code><br>Відповідає формату: ${validFormat ? 'Так' : 'Ні'}`
            );

            // Тест 4: Швидкість генерації
            const startTime = performance.now();
            for (let i = 0; i < 1000; i++) {
                generateRandomSubdomain();
            }
            const endTime = performance.now();
            const duration = endTime - startTime;
            
            addTestResult(
                'Швидкість генерації (1000 субдоменів)',
                duration < 100,
                `Час виконання: ${duration.toFixed(2)}ms<br>Середній час на субдомен: ${(duration/1000).toFixed(4)}ms`
            );

            // Тест 5: Базовий домен
            const baseDomain = getBaseDomain();
            const isValidDomain = baseDomain && baseDomain.includes('.');
            addTestResult(
                'Визначення базового домена',
                isValidDomain,
                `Поточний hostname: <code>${window.location.hostname}</code><br>Базовий домен: <code>${baseDomain}</code>`
            );

            // Тест 6: Повний URL субдомена
            const randomSub = generateRandomSubdomain();
            const fullSubdomain = randomSub + '.' + baseDomain;
            const validUrl = /^[a-z0-9]{6}\.[a-z0-9.-]+$/.test(fullSubdomain);
            addTestResult(
                'Формування повного URL субдомена',
                validUrl,
                `Згенерований субдомен: <code>${fullSubdomain}</code><br>Валідний формат: ${validUrl ? 'Так' : 'Ні'}`
            );

            console.log('✅ Тести завершено');
        }

        function generateBatch() {
            const batchSize = 100;
            const subdomains = [];
            const stats = {
                total: batchSize,
                unique: 0,
                duplicates: 0,
                avgLength: 0,
                minLength: Infinity,
                maxLength: 0
            };

            // Генеруємо субдомени
            for (let i = 0; i < batchSize; i++) {
                const sub = generateRandomSubdomain();
                subdomains.push(sub);
                
                stats.avgLength += sub.length;
                stats.minLength = Math.min(stats.minLength, sub.length);
                stats.maxLength = Math.max(stats.maxLength, sub.length);
            }

            // Рахуємо статистику
            const uniqueSubdomains = [...new Set(subdomains)];
            stats.unique = uniqueSubdomains.length;
            stats.duplicates = batchSize - stats.unique;
            stats.avgLength = (stats.avgLength / batchSize).toFixed(2);

            // Створюємо контент
            const statsContent = `
                <div class="stats">
                    <div class="stat-box">
                        <strong>Всього</strong><br>${stats.total}
                    </div>
                    <div class="stat-box">
                        <strong>Унікальних</strong><br>${stats.unique}
                    </div>
                    <div class="stat-box">
                        <strong>Дублікатів</strong><br>${stats.duplicates}
                    </div>
                    <div class="stat-box">
                        <strong>Середня довжина</strong><br>${stats.avgLength}
                    </div>
                </div>
                <h4>Згенеровані субдомени:</h4>
                <div class="subdomain-list">
                    <pre>${subdomains.slice(0, 50).join(', ')}${subdomains.length > 50 ? '\n... та ще ' + (subdomains.length - 50) + ' субдоменів' : ''}</pre>
                </div>
            `;

            addInfoResult(`Батч з ${batchSize} субдоменів`, statsContent);
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }

        // Автоматичний запуск тестів при завантаженні
        window.onload = function() {
            // Показуємо інформацію про поточний домен
            const infoContent = `
                <p><strong>Поточний домен:</strong> <code>${window.location.hostname}</code></p>
                <p><strong>Базовий домен:</strong> <code>${getBaseDomain()}</code></p>
                <p><strong>Приклад субдомена:</strong> <code>${generateRandomSubdomain()}.${getBaseDomain()}</code></p>
            `;
            addInfoResult('Інформація про домен', infoContent);
            
            runAllTests();
        };
    </script>
</body>
</html>
