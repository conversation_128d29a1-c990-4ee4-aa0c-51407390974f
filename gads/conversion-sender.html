<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Conversion Sender</title>
</head>
<body>

<script>
    /**
     * Get URL parameter value by name
     */
    function getUrlParameter(sParam) {
        var sPageURL = window.location.search.substring(1),
            sURLVariables = sPageURL.split('&'),
            sParameterName,
            i;

        for (i = 0; i < sURLVariables.length; i++) {
            sParameterName = sURLVariables[i].split('=');
            if (sParameterName[0] === sParam) {
                return sParameterName[1] === undefined ? true : decodeURIComponent(sParameterName[1]);
            }
        }
        return false;
    }

    // Get conversion parameters
    var awId = getUrlParameter('aw');
    var conversionLabel = getUrlParameter('label');
    var value = getUrlParameter('value') || '1.0';
    var currency = getUrlParameter('currency') || 'USD';
    
    console.log('🚀 CONVERSION SENDER LOADED');
    console.log('   Domain:', window.location.hostname);
    console.log('   AW ID:', awId);
    console.log('   Label:', conversionLabel);
    console.log('   Value:', value, currency);

    if (awId && conversionLabel) {
        // Load Google Ads gtag
        var gtagScript = document.createElement('script');
        gtagScript.async = true;
        gtagScript.src = 'https://www.googletagmanager.com/gtag/js?id=' + awId;
        document.head.appendChild(gtagScript);
        
        console.log('📡 Loading gtag script for:', awId);

        gtagScript.onload = function() {
            console.log('✅ Gtag script loaded successfully');
            
            // Initialize gtag
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', awId);
            
            console.log('⚙️ Gtag configured for AW ID:', awId);

            // Send conversion
            var conversionData = {
                'send_to': awId + '/' + conversionLabel
            };
            
            // Add value if not 'none'
            if (value !== 'none') {
                conversionData['value'] = parseFloat(value);
                conversionData['currency'] = currency;
            }
            
            gtag('event', 'conversion', conversionData);

            console.log('🎯 CONVERSION SENT!');
            console.log('   From domain:', window.location.hostname);
            console.log('   To:', awId + '/' + conversionLabel);
            if (value !== 'none') {
                console.log('   Value:', value + ' ' + currency);
            }
            console.log('   Data:', conversionData);
            
            // Notify parent window (if in iframe)
            try {
                if (window.parent && window.parent !== window) {
                    window.parent.postMessage({
                        type: 'conversion_sent',
                        domain: window.location.hostname,
                        awId: awId,
                        label: conversionLabel,
                        value: value,
                        currency: currency
                    }, '*');
                }
            } catch(e) {
                console.log('Could not notify parent:', e);
            }
        };
        
        gtagScript.onerror = function() {
            console.log('❌ Failed to load gtag script');
        };
        
    } else {
        console.log('❌ Missing required parameters (aw or label)');
    }

</script>
</body>
</html>
