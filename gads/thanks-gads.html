<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thanks</title>
</head>
<style>
    h1 {
  position: fixed;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -50%);
}
    </style>
<body>
    <h1>Thank you!</h1>
</body>
</html>
<script>
    /**
     * Get URL parameter value by name
     * @param {string} sParam - Parameter name to search for
     * @returns {string|boolean} Parameter value or false if not found
     */
    var getUrlParameter = function getUrlParameter(sParam) {
                var sPageURL = window.location.search.substring(1),
                    sURLVariables = sPageURL.split('&'),
                    sParameterName,
                    i;

                for (i = 0; i < sURLVariables.length; i++) {
                    sParameterName = sURLVariables[i].split('=');

                    if (sParameterName[0] === sParam) {
                        return sParameterName[1] === undefined ? true : decodeURIComponent(sParameterName[1]);
                    }
                }
                return false;
    };

    /**
     * Build URL with additional parameters
     * @param {string} baseUrl - Base URL to append parameters to
     * @param {object} params - Object containing key-value pairs to append
     * @returns {string} URL with appended parameters
     */
    function buildUrlWithParams(baseUrl, params) {
      let url = baseUrl;
      let index = baseUrl.indexOf("?");
      let firstParam = false
      if (index == -1) {
          firstParam = true;
      }
      for (var key in params) {
        if (params.hasOwnProperty(key)) {
          if (firstParam) {
            url = url + "?" + key + "=" + params[key];
            firstParam = false; 
          } else {
            url = url + "&" + key + "=" + params[key];
          }
        }
      }
      return url.toString();
    }

    /**
     * Parse all URL parameters into an object
     * @param {string} url - URL to parse (optional, defaults to current page)
     * @returns {object} Object containing all URL parameters
     */
    function getAllUrlParams(url) {
    var queryString = url ? url.split('?')[1] : window.location.search.slice(1);
    var queryParams = {};
    if (!queryString) {
        return queryParams;
    }
    var pairs = queryString.split('&');
    for (var i = 0; i < pairs.length; i++) {
        var pair = pairs[i].split('=');
        var key = decodeURIComponent(pair[0]);
        var value = decodeURIComponent(pair[1] || '');
        if (queryParams[key]) {
        if (Array.isArray(queryParams[key])) {
            queryParams[key].push(value);
        } else {
            queryParams[key] = [queryParams[key], value];
        }
        } else {
        queryParams[key] = value;
        }
    }

    return queryParams;
    }

    /**
     * Generate random subdomain string (6 characters)
     * @returns {string} Random alphanumeric string for subdomain
     */
    function generateRandomSubdomain() {
        return (Math.random() + 1).toString(36).substring(2, 8);
    }

    // Get all URL parameters and log them
    var params = getAllUrlParams(window.location.href);
    console.log(params);

    // Store 'd' parameter in localStorage for backup
    localStorage.setItem('dd', getUrlParameter('d'));
    
    // Create tracking pixel iframe with random subdomain
    var _ff = document.createElement('iframe');
    _ff.src="https://"+(Math.random() + 1).toString(36).substring(8)+"."+window.location.hostname+"/"+window.location.search;
    _ff.setAttribute("height","1");
    _ff.setAttribute("width","1");
    document.body.appendChild(_ff);

    // Log utm_medium parameter for debugging
    console.log(getUrlParameter("utm_medium"));
    
    
    // Extract and store sub_id_10 from URL parameters (format: AW-XXXXXXXXX/CONVERSION_LABEL)
    var sub_id_10 = getUrlParameter('sub_id_10');
    var aw = '';        // Will store AW portion (e.g., AW-123456789)
    var conv_id = '';   // Will store conversion label portion
    
    if (sub_id_10) {
        // Store in localStorage for backup
        localStorage.setItem('sub_id_10', sub_id_10);
        console.log('Stored sub_id_10:', sub_id_10);
        
        // Split sub_id_10 by "/" to separate AW ID and conversion label
        var parts = sub_id_10.split('/');
        if (parts.length >= 2) {
            aw = parts[0];        // AW-XXXXXXXXX
            conv_id = parts[1];   // CONVERSION_LABEL
            console.log('Split conversion ID - AW:', aw, 'Conv ID:', conv_id);
        }
    } else {
        // Try to get from localStorage if not in URL
        sub_id_10 = localStorage.getItem('sub_id_10');
        if (sub_id_10) {
            var parts = sub_id_10.split('/');
            if (parts.length >= 2) {
                aw = parts[0];
                conv_id = parts[1];
            }
        }
        console.log('Retrieved sub_id_10 from storage:', sub_id_10);
    }

    /**
     * Send postback with sub_id_10 substitution to random subdomain
     * @param {string} subId - The sub_id_10 value to send in postback
     */
    function sendPostback(subId) {
        if (subId) {
            var randomSubdomain = generateRandomSubdomain();
            var postbackUrl = 'https://' + randomSubdomain + '.' + window.location.hostname + '?=true&sub_id_10=' + encodeURIComponent(subId);
            
            // Create hidden iframe to send postback
            var postbackFrame = document.createElement('iframe');
            postbackFrame.src = postbackUrl;
            postbackFrame.style.display = 'none';
            document.body.appendChild(postbackFrame);
            
            console.log('Postback sent:', postbackUrl);
        }
    }

    // Initialize Google Ads tracking with dynamic sub_id_10 from random subdomain
    if (sub_id_10 && aw) {
        // Generate random subdomain for Google Ads conversion
        var randomSubdomain = generateRandomSubdomain();
        
        console.log('=== GOOGLE ADS CONVERSION START ===');
        console.log('Received sub_id_10:', sub_id_10);
        console.log('Parsed AW ID:', aw);
        console.log('Generated random subdomain:', randomSubdomain);
        console.log('Will send conversion from domain:', randomSubdomain + '.' + window.location.hostname);
        
        // Create iframe with random subdomain to send Google Ads conversion
        var gadsFrame = document.createElement('iframe');
        gadsFrame.style.display = 'none';
        gadsFrame.style.width = '1px';
        gadsFrame.style.height = '1px';
        document.body.appendChild(gadsFrame);
        
        // Create Google Ads conversion in iframe with random subdomain
        setTimeout(function() {
            var gadsDoc = gadsFrame.contentWindow.document;
            
            // Override hostname in iframe
            try {
                Object.defineProperty(gadsFrame.contentWindow.location, 'hostname', {
                    writable: false,
                    value: randomSubdomain + '.' + window.location.hostname
                });
                console.log('✅ Successfully overridden hostname to:', randomSubdomain + '.' + window.location.hostname);
            } catch(e) {
                console.log('❌ Cannot override hostname:', e);
            }
            
            // Load Google tag with AW ID in iframe
            var gtag_script = gadsDoc.createElement('script');
            gtag_script.async = true;
            gtag_script.src = 'https://www.googletagmanager.com/gtag/js?id=' + aw;
            gadsDoc.head.appendChild(gtag_script);
            
            console.log('📡 Loading Google Ads script for AW ID:', aw);

            // Initialize gtag and send conversion after script loads
            gtag_script.onload = function() {
                console.log('✅ Google Ads script loaded successfully');
                
                gadsFrame.contentWindow.dataLayer = gadsFrame.contentWindow.dataLayer || [];
                function gtag(){gadsFrame.contentWindow.dataLayer.push(arguments);}
                gtag('js', new Date());
                gtag('config', aw);
                
                console.log('⚙️ Configured Google Ads with AW ID:', aw);

                // Get conversion value and currency from URL parameters or defaults
                var conversionValue = getUrlParameter('value') || 1.0;
                var conversionCurrency = getUrlParameter('currency') || 'USD';
                
                // Uncomment line below to disable value tracking by default (comment out lines above)
                // var conversionValue = getUrlParameter('value') || 'none';
                
                // Send conversion event with full sub_id_10
                var conversionData = {
                    'send_to': sub_id_10
                };
                
                // Add value and currency only if value is not 'none'
                if (conversionValue !== 'none') {
                    conversionData['value'] = parseFloat(conversionValue);
                    conversionData['currency'] = conversionCurrency;
                }
                
                gtag('event', 'conversion', conversionData);

                console.log('🎯 CONVERSION SENT TO GOOGLE ADS:');
                console.log('   From domain:', gadsFrame.contentWindow.location.hostname);
                console.log('   To conversion ID:', sub_id_10);
                if (conversionValue !== 'none') {
                    console.log('   Value:', conversionValue + ' ' + conversionCurrency);
                } else {
                    console.log('   Value: none (no value tracking)');
                }
                console.log('=== GOOGLE ADS CONVERSION END ===');
            };
            
            gtag_script.onerror = function() {
                console.log('❌ Failed to load Google Ads script');
            };
        }, 100);
    } else {
        console.log('❌ Google Ads conversion skipped - missing sub_id_10 or AW ID');
        if (!sub_id_10) console.log('   Missing sub_id_10 parameter');
        if (!aw) console.log('   Failed to parse AW ID from sub_id_10');
    }

    // Send postback to random subdomain
    sendPostback(sub_id_10);

    // Redirect after 3 seconds
    setTimeout(function() {
        const urlParams = new URLSearchParams(window.location.search);
        const url = urlParams.get('link');
        if (url && url != "") {
            // Redirect to provided link with all current parameters
            let paramsUrl = buildUrlWithParams(url, getAllUrlParams(window.location.href));
            console.log("Redirecting with params:", paramsUrl);
            window.location.href = paramsUrl;
        } else {
            // Default redirect if no link parameter provided
            const redirectUrl = "https://www.google.com/";
            console.log("Redirecting to default URL:", redirectUrl);
            window.location.href = redirectUrl;
        }
    }, 3000);
    
</script>