# Деплой на GitHub Pages + Cloudflare

## 🚀 Налаштування GitHub Pages

### Крок 1: Активація GitHub Pages

1. **Налаштування репозиторію**
   - Перейдіть в Settings репозиторію
   - Scroll до розділу "Pages"
   - Source: `Deploy from a branch`
   - Branch: `main`
   - Folder: `/ (root)`

2. **Створення GitHub Actions**
   Створіть файл `.github/workflows/deploy.yml`:

```yaml
name: Deploy to GitHub Pages

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Pages
      uses: actions/configure-pages@v3
      
    - name: Upload artifact
      uses: actions/upload-pages-artifact@v2
      with:
        path: './gads'
        
    - name: Deploy to GitHub Pages
      id: deployment
      uses: actions/deploy-pages@v2
```

### Крок 2: Налаштування Cloudflare

1. **Додайте сайт в Cloudflare**
   - Add Site: `yourdomain.com`
   - Виберіть Free план
   - Змініть nameservers у вашого реєстратора

2. **DNS налаштування**
   ```
   Type: CNAME
   Name: @
   Content: yourusername.github.io
   Proxy: ✅ Proxied

   Type: CNAME
   Name: *
   Content: yourusername.github.io
   Proxy: ✅ Proxied
   ```

3. **SSL/TLS налаштування**
   - SSL/TLS → Overview
   - Encryption mode: `Full`
   - Always Use HTTPS: `On`

### Крок 3: Налаштування кастомного домену

1. **В GitHub Pages**
   - Settings → Pages
   - Custom domain: `yourdomain.com`
   - Enforce HTTPS: ✅

2. **Створіть файл CNAME**
   В корені репозиторію створіть файл `CNAME`:
   ```
   yourdomain.com
   ```

### Крок 4: Cloudflare Page Rules

1. **Wildcard підтримка**
   - Page Rules → Create Page Rule
   - URL: `*.yourdomain.com/*`
   - Settings: `Forwarding URL (301 Redirect)`
   - Destination: `https://yourdomain.com/$2`

2. **Альтернативний підхід - Workers**
   Створіть Cloudflare Worker:

```javascript
addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request))
})

async function handleRequest(request) {
  const url = new URL(request.url)
  const hostname = url.hostname
  
  // Якщо це субдомен, перенаправляємо на основний домен
  if (hostname.includes('.') && !hostname.startsWith('www.')) {
    const subdomain = hostname.split('.')[0]
    const mainDomain = hostname.substring(hostname.indexOf('.') + 1)
    
    // Перенаправляємо на основний домен з збереженням шляху
    const newUrl = `https://${mainDomain}${url.pathname}${url.search}`
    return fetch(newUrl, request)
  }
  
  // Для основного домену - звичайний запит
  return fetch(request)
}
```

## 🧪 Тестування

### Основний домен:
```
https://yourdomain.com/conversion-tracker.html?sub_id_10=AW-123456789/TestLabel&value=100&currency=USD
```

### Wildcard субдомени (через Cloudflare):
```
https://abc123.yourdomain.com/conversion-sender.html
https://def456.yourdomain.com/conversion-sender.html
```

## ⚠️ Обмеження GitHub Pages

### Що НЕ працює:
- Нативна підтримка wildcard субдоменів
- Серверна логіка (тільки статичні файли)
- Custom headers

### Що працює через Cloudflare:
- ✅ Wildcard субдомени (через Workers/Page Rules)
- ✅ SSL сертифікати
- ✅ CDN кешування
- ✅ DDoS захист

## 🔧 Troubleshooting

### Проблема: GitHub Pages не оновлюється
```bash
# Перевірте статус деплою
git push origin main
# Перевірте Actions tab в GitHub
```

### Проблема: Cloudflare не проксує
- Перевірте що DNS записи мають оранжеву хмарку
- Перевірте SSL/TLS налаштування
- Очистіть кеш Cloudflare

### Проблема: Субдомени не працюють
- Перевірте Page Rules або Workers
- Перевірте DNS wildcard запис
- Перевірте що Cloudflare проксує трафік

## 💰 Вартість

### GitHub Pages:
- ✅ Безкоштовно для публічних репозиторіїв
- ✅ 2000 хвилин Actions/місяць

### Cloudflare:
- ✅ Безкоштовний план включає:
  - Необмежені DNS запити
  - SSL сертифікати
  - 100,000 Workers запитів/день

---

**Готово!** Система працює на GitHub Pages + Cloudflare з wildcard підтримкою.
