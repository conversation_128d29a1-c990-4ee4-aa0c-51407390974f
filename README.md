# Keitaro OnlyBot Gray Project

A collection of tracking and conversion scripts for affiliate marketing campaigns.

## Project Structure

```
kz-onlybot-gray/
├── .gitignore           # Git ignore rules
├── CONTEXT.md           # Development history and context
├── README.md            # This file
└── gads/
    ├── thanks-gads.html # Google Ads conversion tracking script
    └── HOSTING_GUIDE.md # Deployment and usage guide
```

## Components

### Google Ads Conversion Tracking (`gads/`)
- **Purpose**: Keitaro S2S postback integration with Google Ads conversion tracking
- **Features**:
  - Automatic Conversion ID splitting (`AW-XXXXX/LABEL`)
  - Random subdomain postbacks
  - localStorage parameter persistence
  - Automatic redirect with parameter forwarding

## Quick Start

### For Google Ads Tracking
1. Navigate to `gads/` directory
2. Read `HOSTING_GUIDE.md` for deployment instructions
3. Deploy `thanks-gads.html` to your hosting
4. Configure Keitaro postback URL

### Usage Example
```
https://your-domain.com/gads/thanks-gads.html?sub_id_10=AW-12345678/CONVERSION_LABEL&link=https://target-url.com
```

## Development

### Adding New Components
1. Create new subdirectory for your component
2. Include component-specific documentation
3. Update this README with new structure
4. Update `CONTEXT.md` with development history

### File Organization
- Root level: General project files and documentation
- Subdirectories: Specific implementations (e.g., `gads/`, `fb/`, `tiktok/`)
- Each subdirectory: Self-contained with own documentation

## Requirements

### General
- HTTPS hosting (required for tracking scripts)
- Static file hosting capability
- No server-side dependencies

### Google Ads Component
- Modern browser with JavaScript support
- localStorage support
- iframe support for postbacks

## Documentation

- **CONTEXT.md**: Full development history and technical details
- **gads/HOSTING_GUIDE.md**: Complete deployment and usage guide
- Component READMEs: Specific implementation details

## Security

- All URL parameters are properly encoded
- Random subdomain generation for load distribution
- HTTPS enforcement for external requests
- No sensitive data persistence

## Support

For issues or questions:
1. Check component-specific documentation
2. Review browser console logs
3. Verify hosting configuration
4. Check parameter format and encoding

## License

This project is for educational and legitimate affiliate marketing purposes only. Use responsibly and in compliance with all applicable laws and platform policies.