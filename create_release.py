#!/usr/bin/env python3
"""
TikTok Ads Extractor - Release Creator
Створює ZIP архів для деплойменту
"""

import zipfile
import os
import json
from datetime import datetime

def create_release():
    # Шляхи
    source_dir = '/Users/<USER>/Documents/GitHub/postbacker/TT-ext'
    
    # Читаємо версію з manifest.json
    manifest_path = os.path.join(source_dir, 'manifest.json')
    with open(manifest_path, 'r', encoding='utf-8') as f:
        manifest = json.load(f)
    
    version = manifest.get('version', '1.0.0')
    zip_name = f'TikTok-Ads-Extractor-v{version}.zip'
    zip_path = f'/Users/<USER>/Documents/GitHub/postbacker/{zip_name}'
    
    # Створюємо архів
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(source_dir):
            # Пропускаємо приховані файли
            dirs[:] = [d for d in dirs if not d.startswith('.')]
            
            for file in files:
                if not file.startswith('.'):
                    file_path = os.path.join(root, file)
                    # Відносний шлях в архіві
                    arc_name = os.path.relpath(file_path, os.path.dirname(source_dir))
                    zipf.write(file_path, arc_name)
                    print(f'Added: {arc_name}')
    
    # Інформація про архів
    size = os.path.getsize(zip_path)
    print(f'\n✅ SUCCESS!')
    print(f'📦 Archive: {zip_name}')
    print(f'📍 Path: {zip_path}')
    print(f'📏 Size: {size:,} bytes')
    print(f'🔢 Version: {version}')
    print(f'⏰ Created: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
    
    return zip_path

if __name__ == '__main__':
    create_release()
