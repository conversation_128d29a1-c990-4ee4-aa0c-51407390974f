# 🎯 TikTok Ads Data Extractor

Chrome розширення для витягування даних з TikTok Ads Manager і передачі їх в Airtable.

## 📦 Що включено

- **manifest.json** - Конфігурація розширення
- **popup.html/js** - Інтерфейс користувача  
- **content.js** - Витягування даних з TikTok Ads
- **background.js** - Фонові процеси та API
- **injected.js** - Доступ до React компонентів
- **styles.css** - Стилі інтерфейсу

## 🚀 Встановлення

### 1. Завантаження в Chrome
1. Відкрийте `chrome://extensions/`
2. Увімкніть "Режим розробника"
3. Натисніть "Завантажити розпаковане"
4. Виберіть папку з розширенням
5. Іконки генеруються автоматично при першому запуску

### 🕵️ Встановлення в антидетект браузери

#### Browser.Vision
1. **Відкрийте Browser.Vision**
2. **Створіть або відкрийте профіль**
3. **В браузері перейдіть до**: `chrome://extensions/`
4. **Увімкніть Developer mode** (правий верхній кут)
5. **Натисніть "Load unpacked"**
6. **Виберіть папку TT-ext**
7. **Розширення з'явиться в списку** - готово!

#### Octo Browser
1. **Запустіть Octo Browser**
2. **Відкрийте профіль** або створіть новий
3. **В адресному рядку введіть**: `chrome://extensions/`
4. **Активуйте "Режим розробника"** (toggle справа вгорі)
5. **Клікніть "Завантажити розпаковане розширення"**
6. **Оберіть папку з TT-ext файлами**
7. **Перевірте що розширення активне** ✅

#### 🔧 Налаштування для антидетект браузерів
- ✅ **Працює з усіма Chromium-based браузерами**
- ✅ **Зберігає налаштування між сесіями**
- ✅ **Не конфліктує з антидетект функціями**
- ⚠️ **Рекомендація**: Тестуйте на одному профілі перед масовим використанням

### 2. Налаштування через інтерфейс
1. Натисніть на іконку розширення
2. Клікніть "Показати налаштування"
3. Заповніть дані Airtable:
   - **API Key** - отримайте на [airtable.com/create/tokens](https://airtable.com/create/tokens)
   - **Base ID** - знайдіть в URL: `airtable.com/appXXXXXX/...`
   - **Table ID** - назва вашої таблиці
4. Натисніть "Тест підключення" для перевірки
5. Збережіть налаштування

## ⚙️ Особливості інтерфейсу

### Покращені налаштування
- **Згорнутий інтерфейс** - налаштування приховані за замовчуванням
- **Огляд статусу** - миттєвий перегляд поточного стану підключення
- **Тест підключення** - перевірка даних перед збереженням
- **Зручне керування** - кнопки очищення та показу/приховання паролів
- **Інтерактивні підказки** - посилання та інструкції прямо в інтерфейсі

### 🏗️ Створення таблиці в Airtable

#### Крок 1: Створіть нову базу
1. Перейдіть на [airtable.com](https://airtable.com)
2. Натисніть "Create a base" → "Start from scratch"
3. Назвіть базу, наприклад: "TikTok Ads Data"

#### Крок 2: Налаштуйте поля таблиці

⚠️ **ВАЖЛИВО**: Не всі поля обов'язкові! Розширення працюватиме навіть з мінімальним набором.

**🔥 Мінімально необхідні поля (БЕЗ НИХ НЕ ПРАЦЮВАТИМЕ):**
| Назва поля | Тип поля | Опції |
|------------|----------|-------|
| `ID` | Single line text | **Primary field** |
| `Type` | Single select | Campaign, AdGroup, Ad |
| `Name` | Single line text | - |
| `Status` | Single select | Active, Paused, Ended |

**💰 Рекомендовані основні метрики:**
| Назва поля | Тип поля | Опції |
|------------|----------|-------|
| `Spend` | Currency | USD, precision 2 |
| `Impressions` | Number | Integer |
| `Clicks` | Number | Integer |
| `CTR` | Percent | 2 decimal places |
| `Conversions` | Number | Integer |

**📊 Повна структура полів (ОПЦІОНАЛЬНО):**

**Основні поля:**
| Назва поля | Тип поля | Опції |
|------------|----------|-------|
| `ID` | Single line text | Primary field |
| `Type` | Single select | Campaign, AdGroup, Ad, Performance |
| `Name` | Single line text | - |
| `Status` | Single select | Active, Paused, Ended, Draft |
| `Objective` | Single line text | Only for campaigns |
| `Budget` | Currency | USD, precision 2 |
| `Spend` | Currency | USD, precision 2 |
| `Impressions` | Number | Integer |
| `Clicks` | Number | Integer |
| `CTR` | Percent | 2 decimal places |
| `CPC` | Currency | USD, precision 4 |
| `CPM` | Currency | USD, precision 2 |
| `Conversions` | Number | Integer |
| `Conversion Rate` | Percent | 2 decimal places |
| `Cost per Conversion` | Currency | USD, precision 4 |
| `ROAS` | Number | 2 decimal places |
| `Reach` | Number | Integer |
| `Frequency` | Number | 2 decimal places |

**Відео метрики:**
| Назва поля | Тип поля | Опції |
|------------|----------|-------|
| `Video Views` | Number | Integer |
| `Video 2s Views` | Number | Integer |
| `Video 6s Views` | Number | Integer |
| `Video 25% Views` | Number | Integer |
| `Video 50% Views` | Number | Integer |
| `Video 75% Views` | Number | Integer |
| `Video 100% Views` | Number | Integer |
| `Video Play Actions` | Number | Integer |
| `Video Watch Time` | Duration | - |
| `Avg Video Play Time` | Duration | - |
| `Video Completion Rate` | Percent | 2 decimal places |

**TikTok-специфічні метрики:**
| Назва поля | Тип поля | Опції |
|------------|----------|-------|
| `Likes` | Number | Integer |
| `Shares` | Number | Integer |
| `Comments` | Number | Integer |
| `Follows` | Number | Integer |
| `Profile Views` | Number | Integer |
| `Engagement Rate` | Percent | 2 decimal places |
| `Social Engagement` | Number | Integer |
| `Unique Users` | Number | Integer |
| `New Users` | Number | Integer |
| `Creative Views` | Number | Integer |

**Додаткові поля:**
| Назва поля | Тип поля | Опції |
|------------|----------|-------|
| `Placement` | Single line text | For adgroups |
| `Audience` | Single line text | For adgroups |
| `Bidding` | Single line text | For adgroups |
| `Format` | Single line text | For ads |
| `Ad Preview` | Attachment | For ads |
| `Raw Data` | Long text | JSON дані |
| `URL` | URL | Source page |
| `Extracted At` | Date | Include time |
| `Source` | Single select | extension, manual, api |

#### Крок 3: Опціональні поля для аналітики
| Назва поля | Тип поля | Формула |
|------------|----------|---------|
| `Cost per Conversion` | Formula | `{Spend} / {Conversions}` |
| `Profit` | Formula | `({Conversions} * 50) - {Spend}` (adjust 50 to your conversion value) |
| `Performance Score` | Formula | `IF({CTR} > 1, "Good", IF({CTR} > 0.5, "Average", "Poor"))` |

#### Крок 4: Налаштування views
Створіть різні представлення:
- **All Data** (за замовчуванням)
- **Campaigns Only** - фільтр Type = Campaign
- **Active Only** - фільтр Status = Active
- **Top Performers** - сортування за CTR або ROAS
- **Recent** - сортування за Extracted At (desc)

## 🧪 Тестування розширення

### 🚀 Швидкий старт для тесту
1. **Встановіть розширення:**
   - Відкрийте `chrome://extensions/`
   - Увімкніть "Developer mode"
   - "Load unpacked" → виберіть папку `TT-ext`

2. **Створіть тестову базу Airtable:**
   - Перейдіть на [airtable.com](https://airtable.com) та створіть акаунт
   - Створіть нову базу "TikTok Test"
   - Додайте мінімальні поля: `ID`, `Type`, `Name`, `Status`, `Spend`
   - Отримайте API token: [airtable.com/create/tokens](https://airtable.com/create/tokens)

3. **Налаштуйте розширення:**
   - Натисніть іконку розширення → "Показати налаштування"
   - API Key: ваш токен
   - Base ID: з URL (appXXXXXX)
   - Table ID: назва таблиці
   - Натисніть "Тест підключення" → "Зберегти"

4. **Протестуйте на TikTok Ads:**
   - Відкрийте [ads.tiktok.com](https://ads.tiktok.com)
   - Перейдіть до будь-якої сторінки з даними
   - Натисніть "Витягти дані" → "Відправити в Airtable"

### ✅ Що очікувати
- Іконка розширення з'явиться в панелі Chrome
- При кліку відкриється сучасний інтерфейс з темною/світлою темою
- Після витягування дані з'являться в попередньому перегляді
- Відправлені дані будуть у вашій Airtable таблиці

## 📊 Використання

### 1. Базове використання
1. Відкрийте ads.tiktok.com
2. Перейдіть до кампаній/груп оголошень/оголошень
3. Натисніть на іконку розширення
4. Налаштуйте Airtable (якщо ще не зроблено):
   - Клікніть "Показати налаштування"
   - Заповніть поля та протестуйте підключення
   - Збережіть налаштування
5. Натисніть "Витягти дані з поточної сторінки"
6. Перегляньте дані та натисніть "Відправити в Airtable"

### 2. Автоматичне витягування
Розширення автоматично витягує дані при:
- Завантаженні сторінки TikTok Ads
- Зміні даних на сторінці
- Навігації між розділами

### 3. Типи даних
- **Campaigns** - Кампанії з бюджетами і показниками
- **AdGroups** - Групи оголошень з таргетингом  
- **Ads** - Окремі оголошення з креативами
- **Performance** - Дані продуктивності з таблиць

## 🔧 Технічні особливості

### Методи витягування даних
1. **DOM scraping** - Аналіз HTML елементів
2. **React hooks** - Доступ до стану компонентів
3. **Network interception** - Перехоплення API запитів
4. **Local storage** - Кешовані дані браузера

### Селектори для витягування
```javascript
// Кампанії
'[data-testid*="campaign"]'
'.campaign-row'
'[class*="campaign-item"]'

// Групи оголошень  
'[data-testid*="adgroup"]'
'.adgroup-row'

// Оголошення
'[data-testid*="ad-"]'
'.ad-row'
```

### API інтеграція
- **Airtable REST API v0**
- Автоматичне батчування (10 записів за раз)
- Retry логіка при помилках
- Валідація даних перед відправкою

## 🛡️ Безпека

### Permissions
- `activeTab` - Доступ до поточної вкладки
- `storage` - Збереження налаштувань
- `scripting` - Виконання скриптів
- `https://ads.tiktok.com/*` - Доступ до TikTok Ads
- `https://api.airtable.com/*` - API Airtable

### Приватність
- API ключі зберігаються локально
- Дані не передаються третім особам
- Працює тільки на ads.tiktok.com

## 🐛 Усунення проблем

### Дані не витягуються
1. Перевірте чи відкрита правильна сторінка TikTok Ads
2. Зачекайте повного завантаження сторінки
3. Спробуйте оновити сторінку
4. Перевірте консоль на помилки (F12)

### Помилки Airtable
1. Перевірте правильність API ключа
2. Переконайтеся що Base ID і Table ID вірні  
3. Перевірте права доступу до таблиці
4. Переконайтеся що поля в таблиці існують

### Розширення не працює
1. Перезавантажте розширення в chrome://extensions/
2. Очистіть кеш браузера
3. Перевірте чи увімкнений "Режим розробника"
4. Подивіться логи в background service worker

## 📈 Розвиток

### Додавання нових селекторів
```javascript
// У content.js додайте нові селектори
const newSelectors = [
  '[data-new-testid]',
  '.new-class-name'
];
```

### Кастомні поля Airtable
```javascript
// У background.js змініть formatDataForAirtable
fields['Custom Field'] = item.customValue || '';
```

### Нові типи даних
```javascript
// Додайте новий тип в extractAllData
const customData = this.extractCustomData();
data.push(...customData);
```

## 🔄 Версії

### v1.0.0 (Базова версія)
- ✅ Базове витягування даних
- ✅ Інтеграція з Airtable
- ✅ Автоматичне виявлення даних
- ✅ Підтримка кампаній, груп, оголошень
- ✅ React компоненти і network API
- ✅ Responsive UI з темною темою

### v1.1.0 (Поточна - Розширені метрики)
- ✅ **Розширені відео метрики**: 2s, 6s, 25%, 50%, 75%, 100% переглядів
- ✅ **TikTok-специфічні метрики**: лайки, шери, коментарі, підписки
- ✅ **Покращені селектори**: підтримка aria-label, title, множинних форматів
- ✅ **Додаткові метрики**: engagement rate, reach, frequency, ROAS
- ✅ **Повна структура Airtable**: 50+ полів для всіх типів даних
- ✅ **Креативні метрики**: preview, creative views для оголошень
- ✅ **Аудиторія метрики**: unique users, new users, profile views

### Планується v1.2.0
- 📋 Експорт в CSV/Excel
- 🔄 Синхронізація з Google Sheets
- 📊 Аналітика та звіти
- 🎯 Розширені фільтри
- 🔔 Сповіщення про зміни
- 🤖 AI-аналіз продуктивності

## 🐛 Можливі баги та вирішення

### ❌ Поширені проблеми

#### 1. Розширення не витягує дані
**Причини:**
- TikTok змінив структуру сторінки
- Дані ще не завантажились
- Неправильні селектори

**Вирішення:**
```javascript
// Перевірте в консолі браузера:
console.log(document.querySelectorAll('[data-testid*="campaign"]'));
// Якщо елементи є - проблема в селекторах
```

#### 2. Помилка підключення до Airtable
**Причини:**
- Неправильний API Key
- Неправильний Base ID або Table ID
- Поля в Airtable не співпадають

**Вирішення:**
- Перевірте API Key на [airtable.com/create/tokens](https://airtable.com/create/tokens)
- Base ID знаходиться в URL: `airtable.com/appXXXXXX`
- Створіть мінімальні поля: ID, Type, Name, Status

#### 3. Дані не відправляються
**Причини:**
- Поля в Airtable мають інші назви
- Неправильний тип поля
- Перевищено ліміт API

**Вирішення:**
- Перевірте назви полів (case-sensitive)
- Використовуйте правильні типи: Number, Currency, Percent
- API ліміт: 5 запитів/секунду

### 🔧 Налаштування для стабільної роботи

#### В антидетект браузерах:
- ✅ **Відключіть блокування JavaScript**
- ✅ **Дозвольте доступ до localStorage**
- ✅ **Не використовуйте агресивні proxy**
- ⚠️ **Тестуйте на одному профілі спочатку**

#### Оптимізація продуктивності:
- 🔄 **Чекайте повного завантаження сторінки**
- ⏱️ **Робіть паузи між витягуваннями (2-3 сек)**
- 📊 **Не витягуйте більше 50 записів за раз**

### 🚨 Критичні помилки

#### Розширення не завантажується:
```
Manifest version not supported
```
**Вирішення:** Використовуйте Chrome 88+ або Chromium-based браузер

#### Доступ заборонено:
```
Access denied to TikTok Ads
```
**Вирішення:** Увійдіть в TikTok Ads Manager перед використанням

## 📞 Підтримка

Для звітів про помилки або пропозицій:
1. Створіть issue в репозиторії
2. Опишіть проблему детально
3. Додайте скріншоти якщо потрібно
4. Вкажіть версію Chrome і розширення
5. **Для антидетект браузерів**: вкажіть назву та версію

**Корисні посилання:**
- [Airtable API Documentation](https://airtable.com/developers/web/api/introduction)
- [Chrome Extensions Developer Guide](https://developer.chrome.com/docs/extensions/)
- [Browser.Vision Support](https://browser.vision/support)
- [Octo Browser Help](https://octobrowser.net/help)

## 📄 Ліцензія

MIT License - використовуйте вільно для особистих і комерційних проектів.

---

**🎉 Готово! Ваше Chrome розширення для TikTok Ads готове до використання в будь-якому Chromium браузері!**

**🕵️ Протестовано з:**
- ✅ Google Chrome
- ✅ Browser.Vision
- ✅ Octo Browser
- ✅ Інші Chromium-based браузери