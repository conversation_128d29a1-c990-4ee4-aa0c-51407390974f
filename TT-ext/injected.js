// Цей скрипт інжектується в контекст сторінки для доступу до React і внутрішніх API
(function() {
  'use strict';

  class TikTokAdsInjector {
    constructor() {
      this.reactData = [];
      this.interceptedRequests = [];
      this.init();
    }

    init() {
      console.log('TikTok Ads Injector loaded');
      
      // Слухаємо повідомлення від content script
      window.addEventListener('message', (event) => {
        if (event.data.type === 'GET_REACT_DATA') {
          this.extractReactData();
        }
      });

      // Перехоплюємо XHR/Fetch запити
      this.interceptNetworkRequests();
      
      // Намагаємося знайти React дані
      this.findReactComponents();
      
      // Моніторимо зміни в DOM
      this.observeReactUpdates();
    }

    extractReactData() {
      try {
        const data = [
          ...this.extractFromReactComponents(),
          ...this.extractFromReduxStore(),
          ...this.extractFromNetworkRequests(),
          ...this.extractFromLocalStorage(),
          ...this.extractFromGlobalVariables()
        ];

        // Відправляємо дані назад до content script
        window.postMessage({
          type: 'REACT_DATA_RESPONSE',
          data: data
        }, '*');

      } catch (error) {
        console.warn('Error extracting React data:', error);
        window.postMessage({
          type: 'REACT_DATA_RESPONSE',
          data: []
        }, '*');
      }
    }

    extractFromReactComponents() {
      const data = [];

      try {
        // Шукаємо React компоненти через фібер
        const reactRoots = document.querySelectorAll('[data-reactroot], #react-root, .react-root');
        
        reactRoots.forEach(root => {
          const fiber = this.findReactFiber(root);
          if (fiber) {
            const reactData = this.extractDataFromFiber(fiber);
            data.push(...reactData);
          }
        });

        // Шукаємо через _reactInternalInstance
        const reactElements = document.querySelectorAll('*');
        reactElements.forEach(element => {
          const reactInstance = element._reactInternalInstance || element.__reactInternalInstance;
          if (reactInstance) {
            const componentData = this.extractDataFromReactInstance(reactInstance);
            data.push(...componentData);
          }
        });

      } catch (error) {
        console.warn('Error extracting from React components:', error);
      }

      return data;
    }

    findReactFiber(element) {
      const key = Object.keys(element).find(key => 
        key.startsWith('__reactInternalInstance') || 
        key.startsWith('__reactFiber')
      );
      return key ? element[key] : null;
    }

    extractDataFromFiber(fiber) {
      const data = [];

      try {
        let current = fiber;
        while (current) {
          if (current.memoizedProps || current.memoizedState) {
            const componentData = this.extractComponentData(current);
            if (componentData) data.push(componentData);
          }
          current = current.child || current.sibling || current.return;
        }
      } catch (error) {
        console.warn('Error traversing fiber:', error);
      }

      return data;
    }

    extractDataFromReactInstance(instance) {
      const data = [];

      try {
        if (instance.memoizedProps) {
          const props = instance.memoizedProps;
          if (props.campaigns || props.adGroups || props.ads || props.data) {
            data.push({
              type: 'react_component',
              source: 'react_instance',
              data: props,
              extractedAt: new Date().toISOString()
            });
          }
        }

        if (instance.memoizedState) {
          const state = instance.memoizedState;
          if (state.campaigns || state.adGroups || state.ads || state.data) {
            data.push({
              type: 'react_state',
              source: 'react_instance',
              data: state,
              extractedAt: new Date().toISOString()
            });
          }
        }
      } catch (error) {
        console.warn('Error extracting from React instance:', error);
      }

      return data;
    }

    extractComponentData(fiber) {
      try {
        const type = fiber.type;
        const props = fiber.memoizedProps;
        const state = fiber.memoizedState;

        // Шукаємо компоненти, що містять дані реклами
        const componentName = type?.displayName || type?.name || 'Unknown';
        
        if (this.isAdsComponent(componentName, props, state)) {
          return {
            type: 'react_component',
            componentName: componentName,
            props: this.sanitizeData(props),
            state: this.sanitizeData(state),
            source: 'fiber',
            extractedAt: new Date().toISOString()
          };
        }
      } catch (error) {
        console.warn('Error extracting component data:', error);
      }

      return null;
    }

    isAdsComponent(name, props, state) {
      // Перевіряємо чи компонент містить дані реклами
      const adsKeywords = ['campaign', 'adgroup', 'ad', 'stats', 'performance', 'metrics'];
      
      const nameMatch = adsKeywords.some(keyword => 
        name.toLowerCase().includes(keyword)
      );

      const propsMatch = props && adsKeywords.some(keyword =>
        Object.keys(props).some(key => key.toLowerCase().includes(keyword))
      );

      const stateMatch = state && adsKeywords.some(keyword =>
        Object.keys(state).some(key => key.toLowerCase().includes(keyword))
      );

      return nameMatch || propsMatch || stateMatch;
    }

    extractFromReduxStore() {
      const data = [];

      try {
        // Шукаємо Redux store
        const reduxStores = [
          window.__REDUX_STORE__,
          window.store,
          window.Redux,
          window.__INITIAL_STATE__
        ];

        reduxStores.forEach(store => {
          if (store && typeof store.getState === 'function') {
            const state = store.getState();
            const adsData = this.extractAdsDataFromObject(state, 'redux_store');
            data.push(...adsData);
          } else if (store && typeof store === 'object') {
            const adsData = this.extractAdsDataFromObject(store, 'redux_state');
            data.push(...adsData);
          }
        });

      } catch (error) {
        console.warn('Error extracting from Redux store:', error);
      }

      return data;
    }

    extractFromNetworkRequests() {
      const data = [];

      try {
        this.interceptedRequests.forEach(request => {
          if (this.isAdsRequest(request.url)) {
            data.push({
              type: 'network_request',
              url: request.url,
              method: request.method,
              data: request.responseData,
              source: 'network',
              extractedAt: new Date().toISOString()
            });
          }
        });
      } catch (error) {
        console.warn('Error extracting from network requests:', error);
      }

      return data;
    }

    extractFromLocalStorage() {
      const data = [];

      try {
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (this.isAdsStorageKey(key)) {
            try {
              const value = JSON.parse(localStorage.getItem(key));
              data.push({
                type: 'local_storage',
                key: key,
                data: value,
                source: 'localStorage',
                extractedAt: new Date().toISOString()
              });
            } catch (parseError) {
              // Не JSON дані
            }
          }
        }
      } catch (error) {
        console.warn('Error extracting from localStorage:', error);
      }

      return data;
    }

    extractFromGlobalVariables() {
      const data = [];

      try {
        // Шукаємо глобальні змінні з даними реклами
        const globalVars = [
          'ttq', 'TikTokAds', 'adsData', 'campaignData', 
          'adGroupData', 'performanceData', '__INITIAL_DATA__',
          '__NUXT__', '__NEXT_DATA__'
        ];

        globalVars.forEach(varName => {
          if (window[varName]) {
            const adsData = this.extractAdsDataFromObject(window[varName], 'global_variable');
            data.push(...adsData);
          }
        });

      } catch (error) {
        console.warn('Error extracting from global variables:', error);
      }

      return data;
    }

    extractAdsDataFromObject(obj, source) {
      const data = [];

      try {
        const adsKeys = ['campaigns', 'adGroups', 'ads', 'stats', 'performance', 'metrics', 'data'];
        
        adsKeys.forEach(key => {
          if (obj[key] && Array.isArray(obj[key])) {
            obj[key].forEach((item, index) => {
              data.push({
                type: key.slice(0, -1), // Видаляємо 's' в кінці
                id: item.id || `${key}_${index}`,
                data: this.sanitizeData(item),
                source: source,
                extractedAt: new Date().toISOString()
              });
            });
          } else if (obj[key] && typeof obj[key] === 'object') {
            const nestedData = this.extractAdsDataFromObject(obj[key], source);
            data.push(...nestedData);
          }
        });

        // Рекурсивно шукаємо в дочірніх об'єктах
        Object.keys(obj).forEach(key => {
          if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
            const nestedData = this.extractAdsDataFromObject(obj[key], source);
            data.push(...nestedData);
          }
        });

      } catch (error) {
        console.warn('Error extracting ads data from object:', error);
      }

      return data;
    }

    interceptNetworkRequests() {
      // Перехоплюємо XMLHttpRequest
      const originalXHROpen = XMLHttpRequest.prototype.open;
      const originalXHRSend = XMLHttpRequest.prototype.send;

      XMLHttpRequest.prototype.open = function(method, url, ...args) {
        this._method = method;
        this._url = url;
        return originalXHROpen.apply(this, [method, url, ...args]);
      };

      XMLHttpRequest.prototype.send = function(data) {
        this.addEventListener('load', () => {
          if (this.readyState === 4 && this.status === 200) {
            try {
              const responseData = JSON.parse(this.responseText);
              this.interceptedRequests.push({
                method: this._method,
                url: this._url,
                requestData: data,
                responseData: responseData,
                timestamp: Date.now()
              });
            } catch (error) {
              // Не JSON відповідь
            }
          }
        });

        return originalXHRSend.apply(this, arguments);
      }.bind(this);

      // Перехоплюємо Fetch
      const originalFetch = window.fetch;
      window.fetch = async function(url, options = {}) {
        const response = await originalFetch(url, options);
        
        if (response.ok && this.isAdsRequest(url)) {
          try {
            const clonedResponse = response.clone();
            const data = await clonedResponse.json();
            
            this.interceptedRequests.push({
              method: options.method || 'GET',
              url: url,
              requestData: options.body,
              responseData: data,
              timestamp: Date.now()
            });
          } catch (error) {
            // Не JSON відповідь
          }
        }

        return response;
      }.bind(this);
    }

    isAdsRequest(url) {
      const adsPatterns = [
        '/api/campaigns',
        '/api/adgroups',
        '/api/ads',
        '/api/stats',
        '/api/performance',
        '/api/reports',
        'campaign',
        'adgroup',
        'creative'
      ];

      return adsPatterns.some(pattern => url.includes(pattern));
    }

    isAdsStorageKey(key) {
      const adsPatterns = ['campaign', 'adgroup', 'ad', 'stats', 'performance', 'tiktok'];
      return adsPatterns.some(pattern => key.toLowerCase().includes(pattern));
    }

    sanitizeData(data) {
      if (!data || typeof data !== 'object') return data;

      try {
        // Видаляємо циклічні посилання і функції
        return JSON.parse(JSON.stringify(data, (key, value) => {
          if (typeof value === 'function') return '[Function]';
          if (typeof value === 'symbol') return '[Symbol]';
          if (value instanceof HTMLElement) return '[HTMLElement]';
          return value;
        }));
      } catch (error) {
        return String(data);
      }
    }

    observeReactUpdates() {
      // Спостерігаємо за оновленнями в React компонентах
      const observer = new MutationObserver((mutations) => {
        let hasReactUpdate = false;

        mutations.forEach(mutation => {
          if (mutation.type === 'childList') {
            mutation.addedNodes.forEach(node => {
              if (node.nodeType === Node.ELEMENT_NODE) {
                const hasReactProps = Object.keys(node).some(key => 
                  key.startsWith('__react') || key.startsWith('_react')
                );
                if (hasReactProps) hasReactUpdate = true;
              }
            });
          }
        });

        if (hasReactUpdate) {
          // Затримка для завантаження даних
          setTimeout(() => {
            this.findReactComponents();
          }, 1000);
        }
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true
      });
    }

    findReactComponents() {
      // Повторно шукаємо React компоненти при оновленнях
      setTimeout(() => {
        this.extractReactData();
      }, 500);
    }
  }

  // Ініціалізуємо інжектор
  new TikTokAdsInjector();

})();