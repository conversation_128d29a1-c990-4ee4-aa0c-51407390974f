class TikTokAdsContentScript {
  constructor() {
    this.isInjected = false;
    this.init();
  }

  init() {
    console.log('TikTok Ads Content Script loaded');
    
    // Інжектуємо скрипт для доступу до React і внутрішніх API
    this.injectScript();
    
    // Слухаємо повідомлення від popup
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      this.handleMessage(request, sender, sendResponse);
      return true; // Асинхронний відповідь
    });

    // Моніторимо зміни на сторінці
    this.observePageChanges();
    
    // Автоматично витягуємо дані при завантаженні
    setTimeout(() => {
      this.autoExtractData();
    }, 3000);
  }

  injectScript() {
    if (this.isInjected) return;
    
    const script = document.createElement('script');
    script.src = chrome.runtime.getURL('injected.js');
    script.onload = () => {
      script.remove();
      this.isInjected = true;
      console.log('Injected script loaded');
    };
    
    (document.head || document.documentElement).appendChild(script);
  }

  async handleMessage(request, sender, sendResponse) {
    try {
      switch (request.action) {
        case 'extractData':
          const data = await this.extractAllData();
          sendResponse({ success: true, data });
          break;
          
        case 'getPageInfo':
          const pageInfo = this.getPageInfo();
          sendResponse({ success: true, pageInfo });
          break;
          
        case 'waitForData':
          await this.waitForDataLoad();
          sendResponse({ success: true });
          break;
          
        default:
          sendResponse({ success: false, error: 'Unknown action' });
      }
    } catch (error) {
      console.error('Content script error:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  async extractAllData() {
    const data = [];
    
    // Чекаємо, поки дані завантажаться
    await this.waitForDataLoad();
    
    // Витягуємо різні типи даних
    const campaigns = this.extractCampaigns();
    const adGroups = this.extractAdGroups();
    const ads = this.extractAds();
    const performance = this.extractPerformanceData();
    
    data.push(...campaigns, ...adGroups, ...ads, ...performance);
    
    // Також спробуємо витягти дані через інжектований скрипт
    const reactData = await this.getReactData();
    if (reactData && reactData.length > 0) {
      data.push(...reactData);
    }
    
    return this.cleanAndValidateData(data);
  }

  extractCampaigns() {
    const campaigns = [];
    
    // Різні селектори для кампаній
    const selectors = [
      '[data-testid*="campaign"]',
      '.campaign-row',
      '[class*="campaign-item"]',
      '[class*="CampaignRow"]',
      '.table-row [data-testid="campaign-name"]',
      'tr:has([data-testid="campaign-name"])'
    ];
    
    selectors.forEach(selector => {
      try {
        const elements = document.querySelectorAll(selector);
        elements.forEach((element, index) => {
          const campaign = this.extractCampaignFromElement(element, index);
          if (campaign) campaigns.push(campaign);
        });
      } catch (error) {
        console.warn(`Error with selector ${selector}:`, error);
      }
    });
    
    return campaigns;
  }

  extractCampaignFromElement(element, index) {
    try {
      const row = element.closest('tr') || element;
      
      return {
        type: 'campaign',
        id: this.getElementText(row, '[data-testid="campaign-id"]') || `campaign_${index}`,
        name: this.getElementText(row, '[data-testid="campaign-name"], .campaign-name, [class*="name"]'),
        status: this.getElementText(row, '[data-testid="campaign-status"], .status, [class*="status"]'),
        objective: this.getElementText(row, '[data-testid="objective"], .objective'),
        budget: this.getElementText(row, '[data-testid="budget"], .budget, [class*="budget"]'),
        
        // Основні метрики
        spend: this.getElementText(row, '[data-testid="spend"], .spend, [class*="spend"], [class*="cost"]'),
        impressions: this.getElementText(row, '[data-testid="impressions"], .impressions, [class*="impression"]'),
        clicks: this.getElementText(row, '[data-testid="clicks"], .clicks, [class*="click"]'),
        ctr: this.getElementText(row, '[data-testid="ctr"], .ctr, [class*="ctr"]'),
        cpc: this.getElementText(row, '[data-testid="cpc"], .cpc, [class*="cpc"]'),
        cpm: this.getElementText(row, '[data-testid="cpm"], .cpm, [class*="cpm"]'),
        
        // Конверсії
        conversions: this.getElementText(row, '[data-testid="conversions"], .conversions, [class*="conversion"]'),
        conversionRate: this.getElementText(row, '[data-testid="conversion-rate"], .conversion-rate, [class*="conv-rate"]'),
        costPerConversion: this.getElementText(row, '[data-testid="cost-per-conversion"], .cost-per-conversion, [class*="cpa"]'),
        
        // Відео метрики
        videoViews: this.getElementText(row, '[data-testid="video-views"], .video-views, [class*="video-view"]'),
        video2sViews: this.getElementText(row, '[data-testid="video-2s-views"], .video-2s-views, [class*="video-2s"]'),
        video6sViews: this.getElementText(row, '[data-testid="video-6s-views"], .video-6s-views, [class*="video-6s"]'),
        video25Views: this.getElementText(row, '[data-testid="video-25-views"], .video-25-views, [class*="video-25"]'),
        video50Views: this.getElementText(row, '[data-testid="video-50-views"], .video-50-views, [class*="video-50"]'),
        video75Views: this.getElementText(row, '[data-testid="video-75-views"], .video-75-views, [class*="video-75"]'),
        video100Views: this.getElementText(row, '[data-testid="video-100-views"], .video-100-views, [class*="video-100"]'),
        
        // Додаткові метрики
        roas: this.getElementText(row, '[data-testid="roas"], .roas, [class*="roas"]'),
        reach: this.getElementText(row, '[data-testid="reach"], .reach, [class*="reach"]'),
        frequency: this.getElementText(row, '[data-testid="frequency"], .frequency, [class*="frequency"]'),
        
        extractedAt: new Date().toISOString(),
        url: window.location.href,
        source: 'content_script'
      };
    } catch (error) {
      console.warn('Error extracting campaign:', error);
      return null;
    }
  }

  extractAdGroups() {
    const adGroups = [];
    
    const selectors = [
      '[data-testid*="adgroup"]',
      '.adgroup-row',
      '[class*="adgroup-item"]',
      '[class*="AdGroupRow"]'
    ];
    
    selectors.forEach(selector => {
      try {
        const elements = document.querySelectorAll(selector);
        elements.forEach((element, index) => {
          const adGroup = this.extractAdGroupFromElement(element, index);
          if (adGroup) adGroups.push(adGroup);
        });
      } catch (error) {
        console.warn(`Error with selector ${selector}:`, error);
      }
    });
    
    return adGroups;
  }

  extractAdGroupFromElement(element, index) {
    try {
      const row = element.closest('tr') || element;
      
      return {
        type: 'adgroup',
        id: this.getElementText(row, '[data-testid="adgroup-id"]') || `adgroup_${index}`,
        name: this.getElementText(row, '[data-testid="adgroup-name"], .adgroup-name, [class*="name"]'),
        status: this.getElementText(row, '[data-testid="adgroup-status"], .status, [class*="status"]'),
        placement: this.getElementText(row, '[data-testid="placement"], .placement'),
        audience: this.getElementText(row, '[data-testid="audience"], .audience'),
        budget: this.getElementText(row, '[data-testid="budget"], .budget, [class*="budget"]'),
        bidding: this.getElementText(row, '[data-testid="bidding"], .bidding, [class*="bid"]'),
        
        // Основні метрики
        spend: this.getElementText(row, '[data-testid="spend"], .spend, [class*="spend"]'),
        impressions: this.getElementText(row, '[data-testid="impressions"], .impressions, [class*="impression"]'),
        clicks: this.getElementText(row, '[data-testid="clicks"], .clicks, [class*="click"]'),
        ctr: this.getElementText(row, '[data-testid="ctr"], .ctr, [class*="ctr"]'),
        cpc: this.getElementText(row, '[data-testid="cpc"], .cpc, [class*="cpc"]'),
        cpm: this.getElementText(row, '[data-testid="cpm"], .cpm, [class*="cpm"]'),
        
        // Конверсії
        conversions: this.getElementText(row, '[data-testid="conversions"], .conversions, [class*="conversion"]'),
        conversionRate: this.getElementText(row, '[data-testid="conversion-rate"], .conversion-rate, [class*="conv-rate"]'),
        costPerConversion: this.getElementText(row, '[data-testid="cost-per-conversion"], .cost-per-conversion, [class*="cpa"]'),
        
        // Відео метрики
        videoViews: this.getElementText(row, '[data-testid="video-views"], .video-views, [class*="video-view"]'),
        video2sViews: this.getElementText(row, '[data-testid="video-2s-views"], .video-2s-views, [class*="video-2s"]'),
        video6sViews: this.getElementText(row, '[data-testid="video-6s-views"], .video-6s-views, [class*="video-6s"]'),
        video25Views: this.getElementText(row, '[data-testid="video-25-views"], .video-25-views, [class*="video-25"]'),
        video50Views: this.getElementText(row, '[data-testid="video-50-views"], .video-50-views, [class*="video-50"]'),
        video75Views: this.getElementText(row, '[data-testid="video-75-views"], .video-75-views, [class*="video-75"]'),
        video100Views: this.getElementText(row, '[data-testid="video-100-views"], .video-100-views, [class*="video-100"]'),
        
        // Додаткові метрики
        roas: this.getElementText(row, '[data-testid="roas"], .roas, [class*="roas"]'),
        reach: this.getElementText(row, '[data-testid="reach"], .reach, [class*="reach"]'),
        frequency: this.getElementText(row, '[data-testid="frequency"], .frequency, [class*="frequency"]'),
        
        extractedAt: new Date().toISOString(),
        url: window.location.href,
        source: 'content_script'
      };
    } catch (error) {
      console.warn('Error extracting adgroup:', error);
      return null;
    }
  }

  extractAds() {
    const ads = [];
    
    const selectors = [
      '[data-testid*="ad-"]',
      '.ad-row',
      '[class*="ad-item"]',
      '[class*="AdRow"]'
    ];
    
    selectors.forEach(selector => {
      try {
        const elements = document.querySelectorAll(selector);
        elements.forEach((element, index) => {
          const ad = this.extractAdFromElement(element, index);
          if (ad) ads.push(ad);
        });
      } catch (error) {
        console.warn(`Error with selector ${selector}:`, error);
      }
    });
    
    return ads;
  }

  extractAdFromElement(element, index) {
    try {
      const row = element.closest('tr') || element;
      
      return {
        type: 'ad',
        id: this.getElementText(row, '[data-testid="ad-id"]') || `ad_${index}`,
        name: this.getElementText(row, '[data-testid="ad-name"], .ad-name, [class*="name"]'),
        status: this.getElementText(row, '[data-testid="ad-status"], .status, [class*="status"]'),
        format: this.getElementText(row, '[data-testid="format"], .format'),
        
        // Основні метрики
        spend: this.getElementText(row, '[data-testid="spend"], .spend, [class*="spend"]'),
        impressions: this.getElementText(row, '[data-testid="impressions"], .impressions, [class*="impression"]'),
        clicks: this.getElementText(row, '[data-testid="clicks"], .clicks, [class*="click"]'),
        ctr: this.getElementText(row, '[data-testid="ctr"], .ctr, [class*="ctr"]'),
        cpc: this.getElementText(row, '[data-testid="cpc"], .cpc, [class*="cpc"]'),
        cpm: this.getElementText(row, '[data-testid="cpm"], .cpm, [class*="cpm"]'),
        
        // Конверсії
        conversions: this.getElementText(row, '[data-testid="conversions"], .conversions, [class*="conversion"]'),
        conversionRate: this.getElementText(row, '[data-testid="conversion-rate"], .conversion-rate, [class*="conv-rate"]'),
        costPerConversion: this.getElementText(row, '[data-testid="cost-per-conversion"], .cost-per-conversion, [class*="cpa"]'),
        
        // Відео метрики
        videoViews: this.getElementText(row, '[data-testid="video-views"], .video-views, [class*="video-view"]'),
        video2sViews: this.getElementText(row, '[data-testid="video-2s-views"], .video-2s-views, [class*="video-2s"]'),
        video6sViews: this.getElementText(row, '[data-testid="video-6s-views"], .video-6s-views, [class*="video-6s"]'),
        video25Views: this.getElementText(row, '[data-testid="video-25-views"], .video-25-views, [class*="video-25"]'),
        video50Views: this.getElementText(row, '[data-testid="video-50-views"], .video-50-views, [class*="video-50"]'),
        video75Views: this.getElementText(row, '[data-testid="video-75-views"], .video-75-views, [class*="video-75"]'),
        video100Views: this.getElementText(row, '[data-testid="video-100-views"], .video-100-views, [class*="video-100"]'),
        
        // Додаткові метрики
        roas: this.getElementText(row, '[data-testid="roas"], .roas, [class*="roas"]'),
        reach: this.getElementText(row, '[data-testid="reach"], .reach, [class*="reach"]'),
        frequency: this.getElementText(row, '[data-testid="frequency"], .frequency, [class*="frequency"]'),
        
        extractedAt: new Date().toISOString(),
        url: window.location.href,
        source: 'content_script'
      };
    } catch (error) {
      console.warn('Error extracting ad:', error);
      return null;
    }
  }

  extractPerformanceData() {
    const performance = [];
    
    // Шукаємо таблиці з даними продуктивності
    const tables = document.querySelectorAll('table, [role="table"], .data-table');
    
    tables.forEach((table, tableIndex) => {
      try {
        const rows = table.querySelectorAll('tr, [role="row"]');
        const headers = this.extractTableHeaders(table);
        
        rows.forEach((row, rowIndex) => {
          if (rowIndex === 0) return; // Пропускаємо заголовок
          
          const cells = row.querySelectorAll('td, th, [role="cell"]');
          if (cells.length === 0) return;
          
          const rowData = {
            type: 'performance',
            id: `perf_${tableIndex}_${rowIndex}`,
            tableIndex,
            rowIndex,
            data: {},
            extractedAt: new Date().toISOString(),
            url: window.location.href,
            source: 'content_script'
          };
          
          cells.forEach((cell, cellIndex) => {
            const header = headers[cellIndex] || `column_${cellIndex}`;
            const value = this.getElementText(cell);
            if (value) {
              rowData.data[header] = value;
            }
          });
          
          if (Object.keys(rowData.data).length > 0) {
            performance.push(rowData);
          }
        });
      } catch (error) {
        console.warn('Error extracting table data:', error);
      }
    });
    
    return performance;
  }

  extractTableHeaders(table) {
    const headers = [];
    const headerRow = table.querySelector('thead tr, tr:first-child, [role="row"]:first-child');
    
    if (headerRow) {
      const headerCells = headerRow.querySelectorAll('th, td, [role="columnheader"]');
      headerCells.forEach(cell => {
        headers.push(this.getElementText(cell) || 'unknown');
      });
    }
    
    return headers;
  }

  getElementText(container, selector = '') {
    try {
      if (!selector) {
        return container.textContent?.trim() || '';
      }
      
      // Розширені селектори для всіх можливих варіантів TikTok Ads
      const expandedSelectors = selector.split(', ').flatMap(sel => [
        sel,
        sel.replace('[data-testid="', '[data-testid*="'),
        sel.replace('[class*="', '[class*="metric-'),
        sel.replace('[class*="', '[class*="table-'),
        sel.replace('[class*="', '[class*="data-'),
        `${sel}[title]`,
        `${sel} span`,
        `${sel} div`,
        `[aria-label*="${sel.replace(/[\[\]"=*]/g, '')}"]`,
        `[title*="${sel.replace(/[\[\]"=*]/g, '')}"]`
      ]);
      
      for (const expandedSelector of expandedSelectors) {
        try {
          const element = container.querySelector(expandedSelector);
          if (element) {
            const text = element.textContent?.trim() || element.title || element.getAttribute('aria-label') || '';
            if (text && text !== '-' && text !== 'N/A' && text !== '--') {
              return text;
            }
          }
        } catch (selectorError) {
          continue;
        }
      }
      
      return '';
    } catch (error) {
      return '';
    }
  }

  async getReactData() {
    return new Promise((resolve) => {
      // Відправляємо запит до інжектованого скрипта
      window.postMessage({ type: 'GET_REACT_DATA' }, '*');
      
      const handler = (event) => {
        if (event.data.type === 'REACT_DATA_RESPONSE') {
          window.removeEventListener('message', handler);
          resolve(event.data.data || []);
        }
      };
      
      window.addEventListener('message', handler);
      
      // Таймаут через 5 секунд
      setTimeout(() => {
        window.removeEventListener('message', handler);
        resolve([]);
      }, 5000);
    });
  }

  async waitForDataLoad() {
    // Чекаємо, поки завантажаться дані
    return new Promise((resolve) => {
      let attempts = 0;
      const maxAttempts = 30; // 15 секунд
      
      const checkForData = () => {
        const hasData = document.querySelectorAll('table tr, [role="row"], .data-row').length > 1;
        
        if (hasData || attempts >= maxAttempts) {
          resolve();
        } else {
          attempts++;
          setTimeout(checkForData, 500);
        }
      };
      
      checkForData();
    });
  }

  observePageChanges() {
    const observer = new MutationObserver((mutations) => {
      let hasSignificantChange = false;
      
      mutations.forEach(mutation => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          hasSignificantChange = true;
        }
      });
      
      if (hasSignificantChange) {
        // Затримка для завантаження даних після зміни
        setTimeout(() => {
          this.autoExtractData();
        }, 2000);
      }
    });
    
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  async autoExtractData() {
    try {
      const data = await this.extractAllData();
      
      if (data.length > 0) {
        // Відправляємо дані в background script для кешування
        chrome.runtime.sendMessage({
          action: 'cacheData',
          data: data,
          timestamp: Date.now()
        });
        
        console.log(`Auto-extracted ${data.length} items from TikTok Ads`);
      }
    } catch (error) {
      console.warn('Auto-extraction failed:', error);
    }
  }

  getPageInfo() {
    return {
      url: window.location.href,
      title: document.title,
      timestamp: Date.now(),
      pathname: window.location.pathname,
      search: window.location.search,
      hash: window.location.hash
    };
  }

  cleanAndValidateData(data) {
    return data.filter(item => {
      // Видаляємо записи без основних даних
      if (!item.type || !item.id) return false;
      
      // Видаляємо дублікати
      const isDuplicate = data.some((other, index) => 
        other !== item && 
        other.type === item.type && 
        other.id === item.id
      );
      
      return !isDuplicate;
    }).map(item => {
      // Очищуємо і нормалізуємо дані
      Object.keys(item).forEach(key => {
        if (typeof item[key] === 'string') {
          item[key] = item[key].trim();
          
          // Видаляємо зайві символи з числових значень
          if (/^[\d,.$%]+$/.test(item[key])) {
            item[key] = item[key].replace(/[,$%]/g, '');
          }
        }
      });
      
      return item;
    });
  }
}

// Ініціалізуємо content script
new TikTokAdsContentScript();