class TikTokAdsContentScript {
  constructor() {
    this.isInjected = false;
    this.init();
  }

  init() {
    console.log('TikTok Ads Content Script loaded');
    
    // Інжектуємо скрипт для доступу до React і внутрішніх API
    this.injectScript();
    
    // Слухаємо повідомлення від popup
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      this.handleMessage(request, sender, sendResponse);
      return true; // Асинхронний відповідь
    });

    // Моніторимо зміни на сторінці
    this.observePageChanges();
    
    // Автоматично витягуємо дані при завантаженні
    setTimeout(() => {
      this.autoExtractData();
    }, 3000);
  }

  injectScript() {
    if (this.isInjected) return;
    
    const script = document.createElement('script');
    script.src = chrome.runtime.getURL('injected.js');
    script.onload = () => {
      script.remove();
      this.isInjected = true;
      console.log('Injected script loaded');
    };
    
    (document.head || document.documentElement).appendChild(script);
  }

  async handleMessage(request, sender, sendResponse) {
    try {
      switch (request.action) {
        case 'extractData':
          const data = await this.extractAllData();
          sendResponse({ success: true, data });
          break;
          
        case 'getPageInfo':
          const pageInfo = this.getPageInfo();
          sendResponse({ success: true, pageInfo });
          break;
          
        case 'waitForData':
          await this.waitForDataLoad();
          sendResponse({ success: true });
          break;
          
        default:
          sendResponse({ success: false, error: 'Unknown action' });
      }
    } catch (error) {
      console.error('Content script error:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  async extractAllData() {
    const data = [];

    console.log('🔍 Starting TikTok Ads data extraction...');

    // Чекаємо, поки дані завантажаться
    await this.waitForDataLoad();

    // Витягуємо різні типи даних
    const campaigns = this.extractCampaigns();
    const adGroups = this.extractAdGroups();
    const ads = this.extractAds();
    const performance = this.extractPerformanceData();

    console.log(`📊 Extracted data counts:`, {
      campaigns: campaigns.length,
      adGroups: adGroups.length,
      ads: ads.length,
      performance: performance.length
    });

    data.push(...campaigns, ...adGroups, ...ads, ...performance);

    // Якщо нічого не знайшли, спробуємо загальний метод витягування даних
    if (data.length === 0) {
      console.log('🔄 No data found with specific selectors, trying fallback extraction...');
      const fallbackData = this.extractFallbackData();
      console.log(`🔄 Fallback extraction found: ${fallbackData.length} items`);
      data.push(...fallbackData);
    }

    // Також спробуємо витягти дані через інжектований скрипт
    const reactData = await this.getReactData();
    if (reactData && reactData.length > 0) {
      console.log(`⚛️ React data extracted: ${reactData.length} items`);
      data.push(...reactData);
    }

    const cleanedData = this.cleanAndValidateData(data);
    console.log(`✅ Final cleaned data: ${cleanedData.length} items`);

    return cleanedData;
  }

  extractCampaigns() {
    const campaigns = [];

    console.log('🎯 Extracting campaigns...');

    // Різні селектори для кампаній - більш універсальні
    const selectors = [
      // Основні селектори для таблиць
      'table tbody tr',
      '[role="table"] [role="row"]',
      '.table-row',
      '.data-row',

      // Специфічні для TikTok Ads
      '[data-testid*="campaign"]',
      '[data-testid*="row"]',
      '[class*="campaign"]',
      '[class*="Campaign"]',
      '[class*="table-row"]',
      '[class*="TableRow"]',

      // Загальні селектори для рядків даних
      'tr[data-row-key]',
      'div[data-row-index]',
      '.ant-table-tbody tr',
      '.table tbody tr'
    ];

    selectors.forEach(selector => {
      try {
        const elements = document.querySelectorAll(selector);
        console.log(`   Selector "${selector}": found ${elements.length} elements`);
        elements.forEach((element, index) => {
          const campaign = this.extractCampaignFromElement(element, index);
          if (campaign) {
            campaigns.push(campaign);
            console.log(`   ✓ Extracted campaign: ${campaign.name || campaign.id}`);
          }
        });
      } catch (error) {
        console.warn(`Error with selector ${selector}:`, error);
      }
    });

    console.log(`🎯 Total campaigns extracted: ${campaigns.length}`);
    return campaigns;
  }

  extractCampaignFromElement(element, index) {
    try {
      const row = element.closest('tr') || element;
      
      return {
        type: 'campaign',
        id: this.getElementText(row, '[data-testid="campaign-id"]') || `campaign_${index}`,
        name: this.getElementText(row, '[data-testid="campaign-name"], .campaign-name, [class*="name"]'),
        status: this.getElementText(row, '[data-testid="campaign-status"], .status, [class*="status"]'),
        objective: this.getElementText(row, '[data-testid="objective"], .objective'),
        budget: this.getElementText(row, '[data-testid="budget"], .budget, [class*="budget"]'),
        
        // Основні метрики
        spend: this.getElementText(row, '[data-testid="spend"], .spend, [class*="spend"], [class*="cost"]'),
        impressions: this.getElementText(row, '[data-testid="impressions"], .impressions, [class*="impression"]'),
        clicks: this.getElementText(row, '[data-testid="clicks"], .clicks, [class*="click"]'),
        ctr: this.getElementText(row, '[data-testid="ctr"], .ctr, [class*="ctr"]'),
        cpc: this.getElementText(row, '[data-testid="cpc"], .cpc, [class*="cpc"]'),
        cpm: this.getElementText(row, '[data-testid="cpm"], .cpm, [class*="cpm"]'),
        
        // Конверсії
        conversions: this.getElementText(row, '[data-testid="conversions"], .conversions, [class*="conversion"]'),
        conversionRate: this.getElementText(row, '[data-testid="conversion-rate"], .conversion-rate, [class*="conv-rate"]'),
        costPerConversion: this.getElementText(row, '[data-testid="cost-per-conversion"], .cost-per-conversion, [class*="cpa"]'),
        
        // Відео метрики - розширені селектори
        videoViews: this.getElementText(row, '[data-testid*="video-view"], [data-testid*="video_view"], .video-views, [class*="video-view"], [class*="videoview"], [title*="Video View"], [aria-label*="Video View"]'),
        video2sViews: this.getElementText(row, '[data-testid*="video-2s"], [data-testid*="video_2s"], [data-testid*="2s-view"], .video-2s-views, [class*="video-2s"], [class*="2s-view"], [title*="2s"], [aria-label*="2s"]'),
        video6sViews: this.getElementText(row, '[data-testid*="video-6s"], [data-testid*="video_6s"], [data-testid*="6s-view"], .video-6s-views, [class*="video-6s"], [class*="6s-view"], [title*="6s"], [aria-label*="6s"]'),
        video25Views: this.getElementText(row, '[data-testid*="video-25"], [data-testid*="video_25"], [data-testid*="25-view"], [data-testid*="25%"], .video-25-views, [class*="video-25"], [class*="25-view"], [title*="25%"], [aria-label*="25%"]'),
        video50Views: this.getElementText(row, '[data-testid*="video-50"], [data-testid*="video_50"], [data-testid*="50-view"], [data-testid*="50%"], .video-50-views, [class*="video-50"], [class*="50-view"], [title*="50%"], [aria-label*="50%"]'),
        video75Views: this.getElementText(row, '[data-testid*="video-75"], [data-testid*="video_75"], [data-testid*="75-view"], [data-testid*="75%"], .video-75-views, [class*="video-75"], [class*="75-view"], [title*="75%"], [aria-label*="75%"]'),
        video100Views: this.getElementText(row, '[data-testid*="video-100"], [data-testid*="video_100"], [data-testid*="100-view"], [data-testid*="100%"], .video-100-views, [class*="video-100"], [class*="100-view"], [title*="100%"], [aria-label*="100%"]'),

        // Додаткові відео метрики
        videoPlayActions: this.getElementText(row, '[data-testid*="video-play"], [data-testid*="play-action"], .video-play-actions, [class*="video-play"], [title*="Play"], [aria-label*="Play"]'),
        videoWatchTime: this.getElementText(row, '[data-testid*="watch-time"], [data-testid*="view-time"], .video-watch-time, [class*="watch-time"], [title*="Watch Time"], [aria-label*="Watch Time"]'),
        avgVideoPlayTime: this.getElementText(row, '[data-testid*="avg-play"], [data-testid*="average-play"], .avg-video-play, [class*="avg-play"], [title*="Average Play"], [aria-label*="Average Play"]'),
        videoCompletionRate: this.getElementText(row, '[data-testid*="completion"], [data-testid*="complete"], .video-completion, [class*="completion"], [title*="Completion"], [aria-label*="Completion"]'),
        
        // Додаткові метрики
        roas: this.getElementText(row, '[data-testid*="roas"], [data-testid*="return"], .roas, [class*="roas"], [title*="ROAS"], [aria-label*="ROAS"]'),
        reach: this.getElementText(row, '[data-testid*="reach"], .reach, [class*="reach"], [title*="Reach"], [aria-label*="Reach"]'),
        frequency: this.getElementText(row, '[data-testid*="frequency"], .frequency, [class*="frequency"], [title*="Frequency"], [aria-label*="Frequency"]'),

        // TikTok-специфічні метрики
        likes: this.getElementText(row, '[data-testid*="like"], [data-testid*="heart"], .likes, [class*="like"], [title*="Like"], [aria-label*="Like"]'),
        shares: this.getElementText(row, '[data-testid*="share"], .shares, [class*="share"], [title*="Share"], [aria-label*="Share"]'),
        comments: this.getElementText(row, '[data-testid*="comment"], .comments, [class*="comment"], [title*="Comment"], [aria-label*="Comment"]'),
        follows: this.getElementText(row, '[data-testid*="follow"], .follows, [class*="follow"], [title*="Follow"], [aria-label*="Follow"]'),
        profileViews: this.getElementText(row, '[data-testid*="profile-view"], [data-testid*="profile_view"], .profile-views, [class*="profile-view"], [title*="Profile View"], [aria-label*="Profile View"]'),

        // Engagement метрики
        engagementRate: this.getElementText(row, '[data-testid*="engagement"], .engagement-rate, [class*="engagement"], [title*="Engagement"], [aria-label*="Engagement"]'),
        socialEngagement: this.getElementText(row, '[data-testid*="social-engagement"], [data-testid*="social_engagement"], .social-engagement, [class*="social-engagement"]'),

        // Аудиторія метрики
        uniqueUsers: this.getElementText(row, '[data-testid*="unique"], [data-testid*="user"], .unique-users, [class*="unique"], [title*="Unique"], [aria-label*="Unique"]'),
        newUsers: this.getElementText(row, '[data-testid*="new-user"], [data-testid*="new_user"], .new-users, [class*="new-user"], [title*="New User"], [aria-label*="New User"]'),
        
        extractedAt: new Date().toISOString(),
        url: window.location.href,
        source: 'content_script'
      };
    } catch (error) {
      console.warn('Error extracting campaign:', error);
      return null;
    }
  }

  extractAdGroups() {
    const adGroups = [];
    
    const selectors = [
      '[data-testid*="adgroup"]',
      '.adgroup-row',
      '[class*="adgroup-item"]',
      '[class*="AdGroupRow"]'
    ];
    
    selectors.forEach(selector => {
      try {
        const elements = document.querySelectorAll(selector);
        elements.forEach((element, index) => {
          const adGroup = this.extractAdGroupFromElement(element, index);
          if (adGroup) adGroups.push(adGroup);
        });
      } catch (error) {
        console.warn(`Error with selector ${selector}:`, error);
      }
    });
    
    return adGroups;
  }

  extractAdGroupFromElement(element, index) {
    try {
      const row = element.closest('tr') || element;
      
      return {
        type: 'adgroup',
        id: this.getElementText(row, '[data-testid="adgroup-id"]') || `adgroup_${index}`,
        name: this.getElementText(row, '[data-testid="adgroup-name"], .adgroup-name, [class*="name"]'),
        status: this.getElementText(row, '[data-testid="adgroup-status"], .status, [class*="status"]'),
        placement: this.getElementText(row, '[data-testid="placement"], .placement'),
        audience: this.getElementText(row, '[data-testid="audience"], .audience'),
        budget: this.getElementText(row, '[data-testid="budget"], .budget, [class*="budget"]'),
        bidding: this.getElementText(row, '[data-testid="bidding"], .bidding, [class*="bid"]'),
        
        // Основні метрики
        spend: this.getElementText(row, '[data-testid="spend"], .spend, [class*="spend"]'),
        impressions: this.getElementText(row, '[data-testid="impressions"], .impressions, [class*="impression"]'),
        clicks: this.getElementText(row, '[data-testid="clicks"], .clicks, [class*="click"]'),
        ctr: this.getElementText(row, '[data-testid="ctr"], .ctr, [class*="ctr"]'),
        cpc: this.getElementText(row, '[data-testid="cpc"], .cpc, [class*="cpc"]'),
        cpm: this.getElementText(row, '[data-testid="cpm"], .cpm, [class*="cpm"]'),
        
        // Конверсії
        conversions: this.getElementText(row, '[data-testid="conversions"], .conversions, [class*="conversion"]'),
        conversionRate: this.getElementText(row, '[data-testid="conversion-rate"], .conversion-rate, [class*="conv-rate"]'),
        costPerConversion: this.getElementText(row, '[data-testid="cost-per-conversion"], .cost-per-conversion, [class*="cpa"]'),
        
        // Відео метрики - розширені селектори
        videoViews: this.getElementText(row, '[data-testid*="video-view"], [data-testid*="video_view"], .video-views, [class*="video-view"], [class*="videoview"], [title*="Video View"], [aria-label*="Video View"]'),
        video2sViews: this.getElementText(row, '[data-testid*="video-2s"], [data-testid*="video_2s"], [data-testid*="2s-view"], .video-2s-views, [class*="video-2s"], [class*="2s-view"], [title*="2s"], [aria-label*="2s"]'),
        video6sViews: this.getElementText(row, '[data-testid*="video-6s"], [data-testid*="video_6s"], [data-testid*="6s-view"], .video-6s-views, [class*="video-6s"], [class*="6s-view"], [title*="6s"], [aria-label*="6s"]'),
        video25Views: this.getElementText(row, '[data-testid*="video-25"], [data-testid*="video_25"], [data-testid*="25-view"], [data-testid*="25%"], .video-25-views, [class*="video-25"], [class*="25-view"], [title*="25%"], [aria-label*="25%"]'),
        video50Views: this.getElementText(row, '[data-testid*="video-50"], [data-testid*="video_50"], [data-testid*="50-view"], [data-testid*="50%"], .video-50-views, [class*="video-50"], [class*="50-view"], [title*="50%"], [aria-label*="50%"]'),
        video75Views: this.getElementText(row, '[data-testid*="video-75"], [data-testid*="video_75"], [data-testid*="75-view"], [data-testid*="75%"], .video-75-views, [class*="video-75"], [class*="75-view"], [title*="75%"], [aria-label*="75%"]'),
        video100Views: this.getElementText(row, '[data-testid*="video-100"], [data-testid*="video_100"], [data-testid*="100-view"], [data-testid*="100%"], .video-100-views, [class*="video-100"], [class*="100-view"], [title*="100%"], [aria-label*="100%"]'),

        // Додаткові відео метрики
        videoPlayActions: this.getElementText(row, '[data-testid*="video-play"], [data-testid*="play-action"], .video-play-actions, [class*="video-play"], [title*="Play"], [aria-label*="Play"]'),
        videoWatchTime: this.getElementText(row, '[data-testid*="watch-time"], [data-testid*="view-time"], .video-watch-time, [class*="watch-time"], [title*="Watch Time"], [aria-label*="Watch Time"]'),
        avgVideoPlayTime: this.getElementText(row, '[data-testid*="avg-play"], [data-testid*="average-play"], .avg-video-play, [class*="avg-play"], [title*="Average Play"], [aria-label*="Average Play"]'),

        // TikTok-специфічні метрики
        likes: this.getElementText(row, '[data-testid*="like"], [data-testid*="heart"], .likes, [class*="like"], [title*="Like"], [aria-label*="Like"]'),
        shares: this.getElementText(row, '[data-testid*="share"], .shares, [class*="share"], [title*="Share"], [aria-label*="Share"]'),
        comments: this.getElementText(row, '[data-testid*="comment"], .comments, [class*="comment"], [title*="Comment"], [aria-label*="Comment"]'),
        follows: this.getElementText(row, '[data-testid*="follow"], .follows, [class*="follow"], [title*="Follow"], [aria-label*="Follow"]'),

        // Додаткові метрики
        roas: this.getElementText(row, '[data-testid*="roas"], [data-testid*="return"], .roas, [class*="roas"], [title*="ROAS"], [aria-label*="ROAS"]'),
        reach: this.getElementText(row, '[data-testid*="reach"], .reach, [class*="reach"], [title*="Reach"], [aria-label*="Reach"]'),
        frequency: this.getElementText(row, '[data-testid*="frequency"], .frequency, [class*="frequency"], [title*="Frequency"], [aria-label*="Frequency"]'),
        engagementRate: this.getElementText(row, '[data-testid*="engagement"], .engagement-rate, [class*="engagement"], [title*="Engagement"], [aria-label*="Engagement"]'),
        
        extractedAt: new Date().toISOString(),
        url: window.location.href,
        source: 'content_script'
      };
    } catch (error) {
      console.warn('Error extracting adgroup:', error);
      return null;
    }
  }

  extractAds() {
    const ads = [];
    
    const selectors = [
      '[data-testid*="ad-"]',
      '.ad-row',
      '[class*="ad-item"]',
      '[class*="AdRow"]'
    ];
    
    selectors.forEach(selector => {
      try {
        const elements = document.querySelectorAll(selector);
        elements.forEach((element, index) => {
          const ad = this.extractAdFromElement(element, index);
          if (ad) ads.push(ad);
        });
      } catch (error) {
        console.warn(`Error with selector ${selector}:`, error);
      }
    });
    
    return ads;
  }

  extractAdFromElement(element, index) {
    try {
      const row = element.closest('tr') || element;
      
      return {
        type: 'ad',
        id: this.getElementText(row, '[data-testid="ad-id"]') || `ad_${index}`,
        name: this.getElementText(row, '[data-testid="ad-name"], .ad-name, [class*="name"]'),
        status: this.getElementText(row, '[data-testid="ad-status"], .status, [class*="status"]'),
        format: this.getElementText(row, '[data-testid="format"], .format'),
        
        // Основні метрики
        spend: this.getElementText(row, '[data-testid="spend"], .spend, [class*="spend"]'),
        impressions: this.getElementText(row, '[data-testid="impressions"], .impressions, [class*="impression"]'),
        clicks: this.getElementText(row, '[data-testid="clicks"], .clicks, [class*="click"]'),
        ctr: this.getElementText(row, '[data-testid="ctr"], .ctr, [class*="ctr"]'),
        cpc: this.getElementText(row, '[data-testid="cpc"], .cpc, [class*="cpc"]'),
        cpm: this.getElementText(row, '[data-testid="cpm"], .cpm, [class*="cpm"]'),
        
        // Конверсії
        conversions: this.getElementText(row, '[data-testid="conversions"], .conversions, [class*="conversion"]'),
        conversionRate: this.getElementText(row, '[data-testid="conversion-rate"], .conversion-rate, [class*="conv-rate"]'),
        costPerConversion: this.getElementText(row, '[data-testid="cost-per-conversion"], .cost-per-conversion, [class*="cpa"]'),
        
        // Відео метрики - розширені селектори
        videoViews: this.getElementText(row, '[data-testid*="video-view"], [data-testid*="video_view"], .video-views, [class*="video-view"], [class*="videoview"], [title*="Video View"], [aria-label*="Video View"]'),
        video2sViews: this.getElementText(row, '[data-testid*="video-2s"], [data-testid*="video_2s"], [data-testid*="2s-view"], .video-2s-views, [class*="video-2s"], [class*="2s-view"], [title*="2s"], [aria-label*="2s"]'),
        video6sViews: this.getElementText(row, '[data-testid*="video-6s"], [data-testid*="video_6s"], [data-testid*="6s-view"], .video-6s-views, [class*="video-6s"], [class*="6s-view"], [title*="6s"], [aria-label*="6s"]'),
        video25Views: this.getElementText(row, '[data-testid*="video-25"], [data-testid*="video_25"], [data-testid*="25-view"], [data-testid*="25%"], .video-25-views, [class*="video-25"], [class*="25-view"], [title*="25%"], [aria-label*="25%"]'),
        video50Views: this.getElementText(row, '[data-testid*="video-50"], [data-testid*="video_50"], [data-testid*="50-view"], [data-testid*="50%"], .video-50-views, [class*="video-50"], [class*="50-view"], [title*="50%"], [aria-label*="50%"]'),
        video75Views: this.getElementText(row, '[data-testid*="video-75"], [data-testid*="video_75"], [data-testid*="75-view"], [data-testid*="75%"], .video-75-views, [class*="video-75"], [class*="75-view"], [title*="75%"], [aria-label*="75%"]'),
        video100Views: this.getElementText(row, '[data-testid*="video-100"], [data-testid*="video_100"], [data-testid*="100-view"], [data-testid*="100%"], .video-100-views, [class*="video-100"], [class*="100-view"], [title*="100%"], [aria-label*="100%"]'),

        // Додаткові відео метрики
        videoPlayActions: this.getElementText(row, '[data-testid*="video-play"], [data-testid*="play-action"], .video-play-actions, [class*="video-play"], [title*="Play"], [aria-label*="Play"]'),
        videoWatchTime: this.getElementText(row, '[data-testid*="watch-time"], [data-testid*="view-time"], .video-watch-time, [class*="watch-time"], [title*="Watch Time"], [aria-label*="Watch Time"]'),
        avgVideoPlayTime: this.getElementText(row, '[data-testid*="avg-play"], [data-testid*="average-play"], .avg-video-play, [class*="avg-play"], [title*="Average Play"], [aria-label*="Average Play"]'),
        videoCompletionRate: this.getElementText(row, '[data-testid*="completion"], [data-testid*="complete"], .video-completion, [class*="completion"], [title*="Completion"], [aria-label*="Completion"]'),

        // TikTok-специфічні метрики для оголошень
        likes: this.getElementText(row, '[data-testid*="like"], [data-testid*="heart"], .likes, [class*="like"], [title*="Like"], [aria-label*="Like"]'),
        shares: this.getElementText(row, '[data-testid*="share"], .shares, [class*="share"], [title*="Share"], [aria-label*="Share"]'),
        comments: this.getElementText(row, '[data-testid*="comment"], .comments, [class*="comment"], [title*="Comment"], [aria-label*="Comment"]'),
        follows: this.getElementText(row, '[data-testid*="follow"], .follows, [class*="follow"], [title*="Follow"], [aria-label*="Follow"]'),
        profileViews: this.getElementText(row, '[data-testid*="profile-view"], [data-testid*="profile_view"], .profile-views, [class*="profile-view"], [title*="Profile View"], [aria-label*="Profile View"]'),

        // Креативні метрики
        creativeViews: this.getElementText(row, '[data-testid*="creative-view"], [data-testid*="creative_view"], .creative-views, [class*="creative-view"]'),
        adPreview: this.getElementText(row, '[data-testid*="preview"], [data-testid*="thumbnail"], .ad-preview, [class*="preview"], [class*="thumbnail"]'),

        // Додаткові метрики
        roas: this.getElementText(row, '[data-testid*="roas"], [data-testid*="return"], .roas, [class*="roas"], [title*="ROAS"], [aria-label*="ROAS"]'),
        reach: this.getElementText(row, '[data-testid*="reach"], .reach, [class*="reach"], [title*="Reach"], [aria-label*="Reach"]'),
        frequency: this.getElementText(row, '[data-testid*="frequency"], .frequency, [class*="frequency"], [title*="Frequency"], [aria-label*="Frequency"]'),
        engagementRate: this.getElementText(row, '[data-testid*="engagement"], .engagement-rate, [class*="engagement"], [title*="Engagement"], [aria-label*="Engagement"]'),
        
        extractedAt: new Date().toISOString(),
        url: window.location.href,
        source: 'content_script'
      };
    } catch (error) {
      console.warn('Error extracting ad:', error);
      return null;
    }
  }

  extractPerformanceData() {
    const performance = [];
    
    // Шукаємо таблиці з даними продуктивності
    const tables = document.querySelectorAll('table, [role="table"], .data-table');
    
    tables.forEach((table, tableIndex) => {
      try {
        const rows = table.querySelectorAll('tr, [role="row"]');
        const headers = this.extractTableHeaders(table);
        
        rows.forEach((row, rowIndex) => {
          if (rowIndex === 0) return; // Пропускаємо заголовок
          
          const cells = row.querySelectorAll('td, th, [role="cell"]');
          if (cells.length === 0) return;
          
          const rowData = {
            type: 'performance',
            id: `perf_${tableIndex}_${rowIndex}`,
            tableIndex,
            rowIndex,
            data: {},
            extractedAt: new Date().toISOString(),
            url: window.location.href,
            source: 'content_script'
          };
          
          cells.forEach((cell, cellIndex) => {
            const header = headers[cellIndex] || `column_${cellIndex}`;
            const value = this.getElementText(cell);
            if (value) {
              rowData.data[header] = value;
            }
          });
          
          if (Object.keys(rowData.data).length > 0) {
            performance.push(rowData);
          }
        });
      } catch (error) {
        console.warn('Error extracting table data:', error);
      }
    });
    
    return performance;
  }

  extractFallbackData() {
    const fallbackData = [];

    console.log('🔄 Starting fallback data extraction...');

    // Пробуємо знайти будь-які таблиці з даними
    const tables = document.querySelectorAll('table, [role="table"], .ant-table, .table');
    console.log(`🔄 Found ${tables.length} potential data tables`);

    tables.forEach((table, tableIndex) => {
      const rows = table.querySelectorAll('tr, [role="row"]');
      console.log(`🔄 Table ${tableIndex}: ${rows.length} rows`);

      if (rows.length > 1) { // Має бути хоча б заголовок + 1 рядок даних
        rows.forEach((row, rowIndex) => {
          if (rowIndex === 0) return; // Пропускаємо заголовок

          const cells = row.querySelectorAll('td, th, [role="cell"]');
          if (cells.length > 2) { // Має бути принаймні 3 колонки
            const rowData = {
              type: 'fallback_data',
              id: `fallback_${tableIndex}_${rowIndex}`,
              tableIndex,
              rowIndex,
              data: {},
              extractedAt: new Date().toISOString(),
              url: window.location.href,
              source: 'fallback_extraction'
            };

            cells.forEach((cell, cellIndex) => {
              const text = cell.textContent?.trim();
              if (text && text !== '-' && text !== '') {
                rowData.data[`column_${cellIndex}`] = text;
              }
            });

            if (Object.keys(rowData.data).length > 0) {
              fallbackData.push(rowData);
              console.log(`🔄 Extracted fallback row: ${Object.keys(rowData.data).length} columns`);
            }
          }
        });
      }
    });

    // Також пробуємо знайти списки з даними
    const lists = document.querySelectorAll('.list-item, .data-item, [class*="item"], [class*="row"]');
    console.log(`🔄 Found ${lists.length} potential list items`);

    lists.forEach((item, index) => {
      const text = item.textContent?.trim();
      if (text && text.length > 10) { // Має бути достатньо тексту
        fallbackData.push({
          type: 'fallback_list_item',
          id: `list_item_${index}`,
          text: text,
          extractedAt: new Date().toISOString(),
          url: window.location.href,
          source: 'fallback_list_extraction'
        });
      }
    });

    console.log(`🔄 Total fallback data extracted: ${fallbackData.length} items`);
    return fallbackData;
  }

  extractTableHeaders(table) {
    const headers = [];
    const headerRow = table.querySelector('thead tr, tr:first-child, [role="row"]:first-child');
    
    if (headerRow) {
      const headerCells = headerRow.querySelectorAll('th, td, [role="columnheader"]');
      headerCells.forEach(cell => {
        headers.push(this.getElementText(cell) || 'unknown');
      });
    }
    
    return headers;
  }

  getElementText(container, selector = '') {
    try {
      if (!selector) {
        return container.textContent?.trim() || '';
      }

      // Спочатку пробуємо прямий селектор
      let element = container.querySelector(selector);
      if (element) {
        const text = this.extractTextFromElement(element);
        if (text) return text;
      }

      // Якщо не знайшли, пробуємо розширені селектори
      const expandedSelectors = this.generateExpandedSelectors(selector);

      for (const expandedSelector of expandedSelectors) {
        try {
          element = container.querySelector(expandedSelector);
          if (element) {
            const text = this.extractTextFromElement(element);
            if (text) return text;
          }
        } catch (selectorError) {
          continue;
        }
      }

      // Якщо нічого не знайшли, пробуємо знайти по тексту в усіх дочірніх елементах
      return this.findTextByPattern(container, selector);

    } catch (error) {
      return '';
    }
  }

  generateExpandedSelectors(selector) {
    const base = selector.split(', ');
    const expanded = [];

    base.forEach(sel => {
      expanded.push(
        sel,
        sel.replace('[data-testid="', '[data-testid*="'),
        sel.replace('[class*="', '[class*="metric-'),
        sel.replace('[class*="', '[class*="table-'),
        sel.replace('[class*="', '[class*="data-'),
        `${sel}[title]`,
        `${sel} span`,
        `${sel} div`,
        `${sel} .value`,
        `${sel} .number`,
        `${sel} .metric`,
        `${sel} .text`,
        `[aria-label*="${sel.replace(/[\[\]"=*]/g, '')}"]`,
        `[title*="${sel.replace(/[\[\]"=*]/g, '')}"]`,
        `[data-tooltip*="${sel.replace(/[\[\]"=*]/g, '')}"]`
      );
    });

    return expanded;
  }

  extractTextFromElement(element) {
    // Спробуємо різні способи отримання тексту
    const text = element.textContent?.trim() ||
                element.title ||
                element.getAttribute('aria-label') ||
                element.getAttribute('data-tooltip') ||
                element.getAttribute('data-value') ||
                element.getAttribute('value') ||
                element.value || '';

    if (text && text !== '-' && text !== 'N/A' && text !== '--' && text !== '0' && text !== '0.00') {
      return this.normalizeMetricValue(text);
    }

    return '';
  }

  findTextByPattern(container, selector) {
    // Витягуємо ключові слова з селектора для пошуку
    const keywords = selector.match(/[\w-]+/g) || [];
    const allElements = container.querySelectorAll('*');

    for (const element of allElements) {
      const text = element.textContent?.trim() || '';
      const hasKeyword = keywords.some(keyword =>
        element.className.toLowerCase().includes(keyword.toLowerCase()) ||
        element.getAttribute('data-testid')?.toLowerCase().includes(keyword.toLowerCase()) ||
        element.getAttribute('aria-label')?.toLowerCase().includes(keyword.toLowerCase())
      );

      if (hasKeyword && text && text !== '-' && text !== 'N/A' && text !== '--') {
        return this.normalizeMetricValue(text);
      }
    }

    return '';
  }

  normalizeMetricValue(value) {
    if (!value || typeof value !== 'string') return '';

    // Видаляємо зайві пробіли та символи
    let normalized = value.trim();

    // Обробляємо числові значення з суфіксами (K, M, B)
    const numberMatch = normalized.match(/^([\d,.-]+)([KMB]?)(%?)$/i);
    if (numberMatch) {
      let [, number, suffix, percent] = numberMatch;
      number = number.replace(/,/g, '');

      if (suffix) {
        const multipliers = { K: 1000, M: 1000000, B: 1000000000 };
        number = parseFloat(number) * (multipliers[suffix.toUpperCase()] || 1);
      }

      return number + (percent || '');
    }

    // Обробляємо відсотки
    if (normalized.includes('%')) {
      return normalized;
    }

    // Обробляємо валюти
    if (normalized.match(/^[\$€£¥₹][\d,.-]+/)) {
      return normalized.replace(/,/g, '');
    }

    // Обробляємо час (duration)
    if (normalized.match(/^\d+:\d+/)) {
      return normalized;
    }

    return normalized;
  }

  async getReactData() {
    return new Promise((resolve) => {
      // Відправляємо запит до інжектованого скрипта
      window.postMessage({ type: 'GET_REACT_DATA' }, '*');
      
      const handler = (event) => {
        if (event.data.type === 'REACT_DATA_RESPONSE') {
          window.removeEventListener('message', handler);
          resolve(event.data.data || []);
        }
      };
      
      window.addEventListener('message', handler);
      
      // Таймаут через 5 секунд
      setTimeout(() => {
        window.removeEventListener('message', handler);
        resolve([]);
      }, 5000);
    });
  }

  async waitForDataLoad() {
    // Чекаємо, поки завантажаться дані
    return new Promise((resolve) => {
      let attempts = 0;
      const maxAttempts = 30; // 15 секунд
      
      const checkForData = () => {
        const hasData = document.querySelectorAll('table tr, [role="row"], .data-row').length > 1;
        
        if (hasData || attempts >= maxAttempts) {
          resolve();
        } else {
          attempts++;
          setTimeout(checkForData, 500);
        }
      };
      
      checkForData();
    });
  }

  observePageChanges() {
    const observer = new MutationObserver((mutations) => {
      let hasSignificantChange = false;
      
      mutations.forEach(mutation => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          hasSignificantChange = true;
        }
      });
      
      if (hasSignificantChange) {
        // Затримка для завантаження даних після зміни
        setTimeout(() => {
          this.autoExtractData();
        }, 2000);
      }
    });
    
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  async autoExtractData() {
    try {
      const data = await this.extractAllData();
      
      if (data.length > 0) {
        // Відправляємо дані в background script для кешування
        chrome.runtime.sendMessage({
          action: 'cacheData',
          data: data,
          timestamp: Date.now()
        });
        
        console.log(`Auto-extracted ${data.length} items from TikTok Ads`);
        console.log('TikTok Ads Data:', data);
      }
    } catch (error) {
      console.warn('Auto-extraction failed:', error);
    }
  }

  getPageInfo() {
    return {
      url: window.location.href,
      title: document.title,
      timestamp: Date.now(),
      pathname: window.location.pathname,
      search: window.location.search,
      hash: window.location.hash
    };
  }

  cleanAndValidateData(data) {
    return data.filter(item => {
      // Видаляємо записи без основних даних
      if (!item.type || !item.id) return false;
      
      // Видаляємо дублікати
      const isDuplicate = data.some(other =>
        other !== item &&
        other.type === item.type &&
        other.id === item.id
      );
      
      return !isDuplicate;
    }).map(item => {
      // Очищуємо і нормалізуємо дані
      Object.keys(item).forEach(key => {
        if (typeof item[key] === 'string') {
          item[key] = item[key].trim();
          
          // Видаляємо зайві символи з числових значень
          if (/^[\d,.$%]+$/.test(item[key])) {
            item[key] = item[key].replace(/[,$%]/g, '');
          }
        }
      });
      
      return item;
    });
  }
}

// Ініціалізуємо content script
new TikTokAdsContentScript();