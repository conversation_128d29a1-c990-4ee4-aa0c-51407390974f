# TikTok Ads Data Extractor - Context & History

## 📋 Поточна версія: 2.0.0

### 🎯 Основна функціональність
- Витягування даних з TikTok Ads Manager
- Автоматична передача в Airtable
- Робота в антидетект браузерах (Browser.Vision, Octo Browser)

### 🔧 Налаштування (СПРОЩЕНО до 2 полів!)
1. **API Key** - токен з Airtable
2. **Airtable URL** - повне посилання на таблицю

### ⚡ Автоматизація
- Base ID і Table ID витягуються автоматично з URL
- Парсинг тільки при paste (не при введенні посимвольно)
- Валідація при збереженні налаштувань

## 🚫 Виправлені проблеми

### v2.0.0 - MAJOR UPDATE
- ❌ Видалено автоматичне відкриття вкладок при встановленні
- ❌ Прибрано дратівливий парсинг "в реальному часі"
- ✅ Спрощено до 2 полів замість 4
- ✅ Розумний парсинг тільки при paste
- ✅ Агресивна блокада відкриття popup.html як вкладки

### v1.1.0 - Попередня версія
- Автоматичний парсинг URL Airtable
- 4 поля: API Key, Base ID, Table ID, Airtable URL
- Проблеми з автоматичним відкриттям вкладок

## 🎨 UI/UX Принципи
- **1-2 кліки максимум** для налаштування
- **Ніяких ручних вводів** Base ID/Table ID
- **Тільки paste URL** - ніякого посимвольного введення
- **Мінімум полів** - максимум автоматизації

## 🔒 Безпека
- Блокування автоматичного відкриття вкладок
- Перехоплення chrome.tabs.create для popup.html
- Валідація URL перед збереженням
- Захист від неправильних налаштувань

## 📁 Структура файлів
- `manifest.json` - конфігурація розширення (v2.0.0)
- `popup.html` - інтерфейс (спрощений до 2 полів)
- `popup.js` - логіка popup (розумний парсинг)
- `background.js` - фонові процеси (блокада вкладок)
- `content.js` - витягування даних з TikTok Ads
- `injected.js` - доступ до React компонентів
- `styles.css` - стилі інтерфейсу

## 🎯 Користувацький досвід
1. Встановлення без відкриття вкладок
2. Клік на іконку розширення
3. Вставка API Key
4. Paste URL таблиці Airtable
5. Збереження - готово!

## 🚀 Деплоймент
- Архівується вся папка TT-ext/
- Версія в назві архіву
- Готово для завантаження в браузер
- Сумісність з антидетект браузерами

## 📝 Примітки для розробки
- Завжди оновлювати версію в manifest.json
- Тестувати в Browser.Vision і Octo Browser
- Мінімізувати кількість полів для користувача
- Максимізувати автоматизацію процесів
