// Скрипт для створення іконок розширення програмно
class IconGenerator {
  constructor() {
    this.canvas = document.createElement('canvas');
    this.ctx = this.canvas.getContext('2d');
  }

  createIcon(size) {
    this.canvas.width = size;
    this.canvas.height = size;
    
    // Очищуємо canvas
    this.ctx.clearRect(0, 0, size, size);
    
    // Створюємо градієнт фон
    const gradient = this.ctx.createLinearGradient(0, 0, size, size);
    gradient.addColorStop(0, '#ff0050');
    gradient.addColorStop(0.5, '#ff4081');
    gradient.addColorStop(1, '#e91e63');
    
    // Малюємо круглий фон
    this.ctx.fillStyle = gradient;
    this.ctx.beginPath();
    this.ctx.arc(size/2, size/2, size/2 - 2, 0, 2 * Math.PI);
    this.ctx.fill();
    
    // Додаємо білу обводку
    this.ctx.strokeStyle = '#ffffff';
    this.ctx.lineWidth = 2;
    this.ctx.stroke();
    
    // Додаємо символ TikTok-стильний
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = `bold ${size * 0.4}px Arial`;
    this.ctx.textAlign = 'center';
    this.ctx.textBaseline = 'middle';
    
    // Малюємо букву "T" з тінню
    this.ctx.shadowColor = 'rgba(0,0,0,0.3)';
    this.ctx.shadowBlur = 2;
    this.ctx.shadowOffsetX = 1;
    this.ctx.shadowOffsetY = 1;
    
    this.ctx.fillText('T', size/2, size/2);
    
    // Скидаємо тінь
    this.ctx.shadowColor = 'transparent';
    this.ctx.shadowBlur = 0;
    this.ctx.shadowOffsetX = 0;
    this.ctx.shadowOffsetY = 0;
    
    // Додаємо маленький індикатор "ADS"
    if (size >= 48) {
      this.ctx.fillStyle = '#ffeb3b';
      this.ctx.font = `bold ${size * 0.12}px Arial`;
      this.ctx.fillText('ADS', size/2, size * 0.8);
    }
    
    return this.canvas.toDataURL('image/png');
  }

  async generateAllIcons() {
    const sizes = [16, 48, 128];
    const icons = {};
    
    for (const size of sizes) {
      const dataUrl = this.createIcon(size);
      icons[`icon${size}`] = dataUrl;
    }
    
    return icons;
  }

  dataURLtoBlob(dataURL) {
    const arr = dataURL.split(',');
    const mime = arr[0].match(/:(.*?);/)[1];
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    return new Blob([u8arr], { type: mime });
  }

  async saveIconsToExtension() {
    const icons = await this.generateAllIcons();
    
    // Зберігаємо іконки в storage для використання розширенням
    await chrome.storage.local.set({ generatedIcons: icons });
    
    return icons;
  }
}

// Експортуємо для використання в popup
if (typeof module !== 'undefined' && module.exports) {
  module.exports = IconGenerator;
} else if (typeof window !== 'undefined') {
  window.IconGenerator = IconGenerator;
}