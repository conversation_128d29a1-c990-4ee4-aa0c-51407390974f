// ANTI-CACHE VERSION: 3.0.0 - TIMESTAMP: ${Date.now()}
console.log('🚀🚀🚀 TikTok Ads Extractor v3.0.0 LOADED! FUCK CACHE! 🚀🚀🚀');
console.log('🔥 BROWSER.VISION CANNOT CACHE THIS SHIT! 🔥');

class TikTokAdsExtractor {
  constructor() {
    console.log('🔧 Constructor called - v3.0.0 - FUCK CACHE!');
    this.extractedData = null;
    this.preventTabOpening();
    this.init();
  }

  preventTabOpening() {
    // БЛОКУЄМО ВІДКРИТТЯ POPUP ЯК ОКРЕМОЇ СТОРІНКИ
    if (window.location.protocol === 'chrome-extension:' &&
        window.location.pathname.includes('popup.html') &&
        !window.chrome?.extension?.getViews) {
      // Якщо popup відкритий як сторінка, а не як popup - закриваємо
      console.log('BLOCKED: popup.html opened as tab, closing...');
      window.close();
      return;
    }
  }

  async init() {
    console.log('🔧 Init called');
    console.log('🔧 DOM ready state:', document.readyState);
    console.log('🔧 Document body:', document.body);
    await this.loadSettings();
    await this.loadVersion();
    this.bindEvents();
    this.updateButtonStates();
    this.updateSettingsSummary();
  }

  async loadSettings() {
    const settings = await chrome.storage.sync.get(['apiKey', 'baseId', 'tableId', 'tableUrl']);

    // Завантажуємо видимі поля
    if (settings.apiKey) document.getElementById('apiKey').value = settings.apiKey;
    if (settings.tableUrl) document.getElementById('tableUrl').value = settings.tableUrl;

    // Завантажуємо приховані поля
    if (settings.baseId) document.getElementById('baseId').value = settings.baseId;
    if (settings.tableId) document.getElementById('tableId').value = settings.tableId;

    // Якщо є URL але немає прихованих полів - парсимо
    if (settings.tableUrl && (!settings.baseId || !settings.tableId)) {
      this.parseUrlOnPaste();
    }
  }

  async loadVersion() {
    try {
      const manifest = chrome.runtime.getManifest();
      const version = manifest.version;

      // Оновлюємо версію в header badge
      const versionBadge = document.getElementById('versionBadge');
      if (versionBadge) {
        versionBadge.textContent = `v${version}`;
      }

      // Оновлюємо версію в footer
      const footerVersion = document.getElementById('footerVersion');
      if (footerVersion) {
        footerVersion.textContent = version;
      }
    } catch (error) {
      console.warn('Could not load version from manifest:', error);
    }
  }

  bindEvents() {
    // Основні дії
    document.getElementById('saveSettings').addEventListener('click', () => this.saveSettings());
    document.getElementById('extractData').addEventListener('click', () => this.extractData());
    document.getElementById('sendToAirtable').addEventListener('click', () => this.sendToAirtable());
    
    // Налаштування
    const toggleBtn = document.getElementById('toggleSettings');
    console.log('🔧 Toggle button found:', toggleBtn);
    if (toggleBtn) {
      toggleBtn.addEventListener('click', () => {
        console.log('🔧 Toggle button clicked!');
        this.toggleSettingsPanel();
      });
    } else {
      console.error('🔧 Toggle button not found!');
    }
    document.getElementById('testConnection').addEventListener('click', () => this.testConnection());
    
    // Кнопки очищення
    document.getElementById('clearApiKey').addEventListener('click', () => this.clearField('apiKey'));
    document.getElementById('clearTableUrl').addEventListener('click', () => this.clearTableUrl());

    // ПАРСИНГ ТІЛЬКИ ПРИ PASTE - БО ХТО БЛЯТЬ ВВОДИТЬ URL РУКАМИ?!
    document.getElementById('tableUrl').addEventListener('paste', () => {
      setTimeout(() => this.parseUrlOnPaste(), 100); // Затримка щоб paste встиг відбутися
    });

    // Показати/сховати API ключ
    document.getElementById('toggleApiKey').addEventListener('click', () => this.toggleApiKeyVisibility());

    // Оновлення summary тільки для API Key
    document.getElementById('apiKey').addEventListener('input', () => this.updateSettingsSummary());
  }

  async saveSettings() {
    const apiKey = document.getElementById('apiKey').value.trim();
    const tableUrl = document.getElementById('tableUrl').value.trim();

    if (!apiKey) {
      this.showStatus('❌ Введіть API Key!', 'error');
      return;
    }

    if (!tableUrl) {
      this.showStatus('❌ Вставте URL таблиці Airtable!', 'error');
      return;
    }

    // Автоматично парсимо URL
    const parsedData = this.extractAirtableData(tableUrl);

    if (!parsedData.baseId || !parsedData.tableId) {
      this.showStatus('❌ Невірний URL Airtable! Перевірте посилання.', 'error');
      return;
    }

    // Зберігаємо все
    await chrome.storage.sync.set({
      apiKey,
      tableUrl,
      baseId: parsedData.baseId,
      tableId: parsedData.tableId
    });

    // Оновлюємо приховані поля
    document.getElementById('baseId').value = parsedData.baseId;
    document.getElementById('tableId').value = parsedData.tableId;

    this.showStatus('✅ Налаштування збережено! Готово до роботи!', 'success');
    this.updateButtonStates();
    this.updateSettingsSummary();
  }

  async extractData() {
    this.showStatus('🔄 Витягування даних...', 'loading');
    
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      if (!tab.url.includes('ads.tiktok.com')) {
        throw new Error('Відкрийте сторінку TikTok Ads Manager');
      }

      const results = await chrome.scripting.executeScript({
        target: { tabId: tab.id },
        function: this.extractTikTokData
      });

      this.extractedData = results[0].result;
      
      if (this.extractedData && this.extractedData.length > 0) {
        this.displayExtractedData();
        this.showStatus(`✅ Витягнуто ${this.extractedData.length} записів`, 'success');
        this.updateButtonStates();
      } else {
        throw new Error('Дані не знайдено на поточній сторінці');
      }
    } catch (error) {
      this.showStatus(`❌ Помилка: ${error.message}`, 'error');
    }
  }

  extractTikTokData() {
    const data = [];
    
    // Селектори для різних типів даних TikTok Ads
    const campaignRows = document.querySelectorAll('[data-testid="campaign-row"], .campaign-row, [class*="campaign"]');
    const adGroupRows = document.querySelectorAll('[data-testid="adgroup-row"], .adgroup-row, [class*="adgroup"]');
    const adRows = document.querySelectorAll('[data-testid="ad-row"], .ad-row, [class*="ad-item"]');
    
    // Функція для витягування тексту з елементу
    const getText = (element) => element ? element.textContent.trim() : '';
    
    // Функція для пошуку числових значень
    const getNumber = (text) => {
      const match = text.match(/[\d,]+\.?\d*/);
      return match ? parseFloat(match[0].replace(/,/g, '')) : null;
    };

    // Витягування даних кампаній
    campaignRows.forEach((row, index) => {
      try {
        const cells = row.querySelectorAll('td, div[class*="cell"], [class*="column"]');
        
        const campaignData = {
          type: 'campaign',
          id: `campaign_${index}`,
          name: getText(row.querySelector('[class*="name"], [data-testid*="name"]')) || `Campaign ${index + 1}`,
          status: getText(row.querySelector('[class*="status"], [data-testid*="status"]')),
          budget: getText(row.querySelector('[class*="budget"], [data-testid*="budget"]')),
          spend: getText(row.querySelector('[class*="spend"], [class*="cost"]')),
          impressions: getText(row.querySelector('[class*="impression"], [data-testid*="impression"]')),
          clicks: getText(row.querySelector('[class*="click"], [data-testid*="click"]')),
          ctr: getText(row.querySelector('[class*="ctr"], [data-testid*="ctr"]')),
          cpc: getText(row.querySelector('[class*="cpc"], [data-testid*="cpc"]')),
          cpm: getText(row.querySelector('[class*="cpm"], [data-testid*="cpm"]')),
          conversions: getText(row.querySelector('[class*="conversion"], [data-testid*="conversion"]')),
          extractedAt: new Date().toISOString(),
          url: window.location.href
        };
        
        data.push(campaignData);
      } catch (error) {
        console.warn('Error extracting campaign data:', error);
      }
    });

    // Витягування даних груп оголошень
    adGroupRows.forEach((row, index) => {
      try {
        const adGroupData = {
          type: 'adgroup',
          id: `adgroup_${index}`,
          name: getText(row.querySelector('[class*="name"], [data-testid*="name"]')) || `AdGroup ${index + 1}`,
          status: getText(row.querySelector('[class*="status"], [data-testid*="status"]')),
          budget: getText(row.querySelector('[class*="budget"], [data-testid*="budget"]')),
          spend: getText(row.querySelector('[class*="spend"], [class*="cost"]')),
          impressions: getText(row.querySelector('[class*="impression"], [data-testid*="impression"]')),
          clicks: getText(row.querySelector('[class*="click"], [data-testid*="click"]')),
          extractedAt: new Date().toISOString(),
          url: window.location.href
        };
        
        data.push(adGroupData);
      } catch (error) {
        console.warn('Error extracting adgroup data:', error);
      }
    });

    // Витягування даних оголошень
    adRows.forEach((row, index) => {
      try {
        const adData = {
          type: 'ad',
          id: `ad_${index}`,
          name: getText(row.querySelector('[class*="name"], [data-testid*="name"]')) || `Ad ${index + 1}`,
          status: getText(row.querySelector('[class*="status"], [data-testid*="status"]')),
          spend: getText(row.querySelector('[class*="spend"], [class*="cost"]')),
          impressions: getText(row.querySelector('[class*="impression"], [data-testid*="impression"]')),
          clicks: getText(row.querySelector('[class*="click"], [data-testid*="click"]')),
          extractedAt: new Date().toISOString(),
          url: window.location.href
        };
        
        data.push(adData);
      } catch (error) {
        console.warn('Error extracting ad data:', error);
      }
    });

    // Якщо стандартні селектори не спрацювали, пробуємо загальний підхід
    if (data.length === 0) {
      const allRows = document.querySelectorAll('tr, [role="row"], .table-row, [class*="row"]');
      
      allRows.forEach((row, index) => {
        const cells = row.querySelectorAll('td, th, div, span');
        if (cells.length >= 3) {
          const rowData = {
            type: 'generic',
            id: `row_${index}`,
            data: Array.from(cells).map(cell => getText(cell)).filter(text => text.length > 0),
            extractedAt: new Date().toISOString(),
            url: window.location.href
          };
          
          if (rowData.data.length > 0) {
            data.push(rowData);
          }
        }
      });
    }

    return data;
  }

  displayExtractedData() {
    const container = document.getElementById('dataContainer');
    
    if (!this.extractedData || this.extractedData.length === 0) {
      container.innerHTML = '<p class="no-data">Дані не знайдено</p>';
      return;
    }

    const dataHtml = this.extractedData.map(item => `
      <div class="data-item">
        <div class="data-header">
          <span class="data-type">${item.type.toUpperCase()}</span>
          <span class="data-name">${item.name || item.id}</span>
        </div>
        <div class="data-details">
          ${Object.entries(item)
            .filter(([key, value]) => key !== 'type' && key !== 'id' && value)
            .map(([key, value]) => `<span class="data-field"><strong>${key}:</strong> ${value}</span>`)
            .join('')}
        </div>
      </div>
    `).join('');

    container.innerHTML = dataHtml;
  }

  async sendToAirtable() {
    if (!this.extractedData || this.extractedData.length === 0) {
      this.showStatus('❌ Спочатку витягніть дані', 'error');
      return;
    }

    const settings = await chrome.storage.sync.get(['apiKey', 'baseId', 'tableId']);
    
    if (!settings.apiKey || !settings.baseId || !settings.tableId) {
      this.showStatus('❌ Налаштуйте підключення до Airtable', 'error');
      return;
    }

    this.showStatus('📤 Відправка в Airtable...', 'loading');

    try {
      const records = this.extractedData.map(item => ({
        fields: {
          'Type': item.type,
          'Name': item.name || item.id,
          'Status': item.status || '',
          'Data': JSON.stringify(item),
          'Extracted At': item.extractedAt,
          'URL': item.url
        }
      }));

      const response = await fetch(`https://api.airtable.com/v0/${settings.baseId}/${settings.tableId}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${settings.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ records })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error?.message || 'Помилка API Airtable');
      }

      const result = await response.json();
      this.showStatus(`✅ Успішно відправлено ${result.records.length} записів`, 'success');
      
    } catch (error) {
      this.showStatus(`❌ Помилка відправки: ${error.message}`, 'error');
    }
  }

  updateButtonStates() {
    const extractBtn = document.getElementById('extractData');
    const sendBtn = document.getElementById('sendToAirtable');
    
    sendBtn.disabled = !this.extractedData || this.extractedData.length === 0;
  }

  showStatus(message, type) {
    const statusDiv = document.getElementById('status');
    statusDiv.textContent = message;
    statusDiv.className = `status ${type}`;
    statusDiv.classList.remove('hidden');

    if (type === 'success' || type === 'error') {
      setTimeout(() => {
        statusDiv.classList.add('hidden');
      }, 5000);
    }
  }

  showConnectionStatus(message, type) {
    const statusDiv = document.getElementById('connectionStatus');
    statusDiv.textContent = message;
    statusDiv.className = `connection-status ${type}`;
    statusDiv.classList.remove('hidden');

    if (type === 'success' || type === 'error') {
      setTimeout(() => {
        statusDiv.classList.add('hidden');
      }, 3000);
    }
  }

  toggleSettingsPanel() {
    console.log('🔧🔧🔧 toggleSettingsPanel called! 🔧🔧🔧');

    const panel = document.getElementById('settingsPanel');
    const toggleText = document.getElementById('toggleText');
    const toggleIcon = document.getElementById('toggleIcon');

    console.log('🔧 Elements found:', { panel, toggleText, toggleIcon });
    console.log('🔧 Panel exists:', !!panel);
    console.log('🔧 Panel classList:', panel?.classList);
    console.log('🔧 Panel has hidden class:', panel?.classList.contains('hidden'));
    console.log('🔧 Panel style display:', panel?.style.display);
    console.log('🔧 Panel computed style:', window.getComputedStyle(panel)?.display);

    if (panel && panel.classList.contains('hidden')) {
      console.log('🔧 REMOVING HIDDEN CLASS');
      panel.classList.remove('hidden');
      if (toggleText) toggleText.textContent = 'Сховати налаштування';
      if (toggleIcon) toggleIcon.textContent = '🙈';
      console.log('🔧 Panel shown - new classList:', panel.classList);
    } else if (panel) {
      console.log('🔧 ADDING HIDDEN CLASS');
      panel.classList.add('hidden');
      if (toggleText) toggleText.textContent = 'Показати налаштування';
      if (toggleIcon) toggleIcon.textContent = '👁️';
      console.log('🔧 Panel hidden - new classList:', panel.classList);
    } else {
      console.error('🔧 Panel not found!');
    }
  }

  async testConnection() {
    const apiKey = document.getElementById('apiKey').value;
    const baseId = document.getElementById('baseId').value;
    const tableId = document.getElementById('tableId').value;

    if (!apiKey || !baseId || !tableId) {
      this.showConnectionStatus('❌ Заповніть всі поля', 'error');
      return;
    }

    this.showConnectionStatus('🔄 Тестування...', 'loading');

    try {
      const response = await chrome.runtime.sendMessage({
        action: 'validateAirtableConnection',
        settings: { apiKey, baseId, tableId }
      });

      if (response.success) {
        this.showConnectionStatus('✅ Підключення успішне!', 'success');
      } else {
        this.showConnectionStatus(`❌ ${response.error}`, 'error');
      }
    } catch (error) {
      this.showConnectionStatus(`❌ Помилка: ${error.message}`, 'error');
    }
  }

  clearField(fieldId) {
    document.getElementById(fieldId).value = '';
    this.updateSettingsSummary();
  }

  clearTableUrl() {
    document.getElementById('tableUrl').value = '';
    document.getElementById('baseId').value = '';
    document.getElementById('tableId').value = '';
    this.updateSettingsSummary();
    this.showStatus('🗑️ URL очищено', 'success');
  }

  parseTableUrl() {
    const tableUrl = document.getElementById('tableUrl').value.trim();

    if (!tableUrl) {
      this.showStatus('❌ Введіть URL таблиці Airtable', 'error');
      return;
    }

    try {
      const parsedData = this.extractAirtableData(tableUrl);

      if (parsedData.baseId) {
        document.getElementById('baseId').value = parsedData.baseId;
      }

      if (parsedData.tableId) {
        document.getElementById('tableId').value = parsedData.tableId;
      }

      if (parsedData.baseId || parsedData.tableId) {
        this.showStatus('✅ Дані витягнуто з URL!', 'success');
        this.updateSettingsSummary();
      } else {
        this.showStatus('❌ Не вдалося витягти дані з URL', 'error');
      }
    } catch (error) {
      this.showStatus(`❌ Помилка парсингу URL: ${error.message}`, 'error');
    }
  }

  parseUrlOnPaste() {
    const tableUrl = document.getElementById('tableUrl').value.trim();

    if (!tableUrl) return;

    // ПАРСИМО ТІЛЬКИ ЯКЩО ЦЕ AIRTABLE URL
    if (tableUrl.includes('airtable.com')) {
      try {
        const parsedData = this.extractAirtableData(tableUrl);

        if (parsedData.baseId && parsedData.tableId) {
          // ВСЕ ЗНАЙШЛИ - ЗАПОВНЮЄМО І РАДІЄМО
          document.getElementById('baseId').value = parsedData.baseId;
          document.getElementById('tableId').value = parsedData.tableId;
          this.updateSettingsSummary();
          this.showStatus('✅ URL розпізнано! Готово до збереження!', 'success');
        }
      } catch (error) {
        // Тихо ігноруємо помилки при paste
      }
    }
  }

  extractAirtableData(url) {
    const result = { baseId: null, tableId: null };

    console.log('Parsing URL:', url);

    // Витягуємо Base ID (app + 14 символів)
    const baseIdMatch = url.match(/app[a-zA-Z0-9]{14}/);
    if (baseIdMatch) {
      result.baseId = baseIdMatch[0];
      console.log('Found Base ID:', result.baseId);
    }

    // Витягуємо Table ID (tbl + 14 символів)
    const tableIdMatch = url.match(/tbl[a-zA-Z0-9]{14}/);
    if (tableIdMatch) {
      result.tableId = tableIdMatch[0];
      console.log('Found Table ID:', result.tableId);
    }

    // Альтернативний пошук Base ID без "app" префіксу
    if (!result.baseId) {
      const altBaseMatch = url.match(/airtable\.com\/([a-zA-Z0-9]{17})/);
      if (altBaseMatch) {
        result.baseId = altBaseMatch[1];
        console.log('Found alternative Base ID:', result.baseId);
      }
    }

    console.log('Extraction result:', result);
    return result;
  }

  toggleApiKeyVisibility() {
    const apiKeyInput = document.getElementById('apiKey');
    const toggleBtn = document.getElementById('toggleApiKey');
    
    if (apiKeyInput.type === 'password') {
      apiKeyInput.type = 'text';
      toggleBtn.textContent = '🙈';
      toggleBtn.title = 'Сховати';
    } else {
      apiKeyInput.type = 'password';
      toggleBtn.textContent = '👁️';
      toggleBtn.title = 'Показати';
    }
  }

  updateSettingsSummary() {
    const apiKey = document.getElementById('apiKey').value;
    const baseId = document.getElementById('baseId').value;
    const tableId = document.getElementById('tableId').value;

    const statusEl = document.getElementById('summaryStatus');
    const baseEl = document.getElementById('summaryBase');
    const tableEl = document.getElementById('summaryTable');

    // Статус
    if (apiKey && baseId && tableId) {
      statusEl.textContent = 'Налаштовано ✅';
      statusEl.className = 'summary-value configured';
    } else {
      statusEl.textContent = 'Не налаштовано ❌';
      statusEl.className = 'summary-value not-configured';
    }

    // База
    if (baseId) {
      baseEl.textContent = baseId.length > 12 ? baseId.substring(0, 12) + '...' : baseId;
      baseEl.className = 'summary-value';
    } else {
      baseEl.textContent = '-';
      baseEl.className = 'summary-value empty';
    }

    // Таблиця
    if (tableId) {
      tableEl.textContent = tableId.length > 15 ? tableId.substring(0, 15) + '...' : tableId;
      tableEl.className = 'summary-value';
    } else {
      tableEl.textContent = '-';
      tableEl.className = 'summary-value empty';
    }
  }
}

// Ініціалізація після завантаження DOM
document.addEventListener('DOMContentLoaded', () => {
  new TikTokAdsExtractor();
});