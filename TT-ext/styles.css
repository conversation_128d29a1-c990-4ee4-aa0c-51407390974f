/* TikTok Ads Extractor - Modern UI */

/* CSS Variables for theming */
:root {
  --primary-color: #ff0050;
  --primary-dark: #e6004a;
  --secondary-color: #6c757d;
  --success-color: #10b981;
  --error-color: #ef4444;
  --warning-color: #f59e0b;
  --info-color: #3b82f6;
  
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-card: #ffffff;
  
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;
  
  --border-color: #e2e8f0;
  --border-focus: #ff0050;
  
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
}

/* Dark theme */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-card: #1e293b;
    
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    
    --border-color: #334155;
  }
}

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-primary);
  background: var(--bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Container */
.container {
  width: 420px;
  max-height: 640px;
  background: var(--bg-card);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  overflow: hidden;
  border: 1px solid var(--border-color);
}

/* Header with gradient */
.header {
  background: linear-gradient(135deg, #ff0050 0%, #ff4081 50%, #e91e63 100%);
  color: white;
  padding: 24px 20px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="1" fill="white" opacity="0.1"/><circle cx="80" cy="40" r="1" fill="white" opacity="0.1"/><circle cx="40" cy="80" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.header h1 {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 4px;
  position: relative;
  z-index: 1;
}

.header p {
  font-size: 13px;
  opacity: 0.9;
  font-weight: 400;
  position: relative;
  z-index: 1;
}

/* Section styles */
.settings,
.actions,
.data-preview {
  padding: 20px;
  border-bottom: 1px solid var(--border-color);
}

.settings:last-child,
.actions:last-child,
.data-preview:last-child {
  border-bottom: none;
}

.settings h3,
.actions h3,
.data-preview h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Settings panel styles */
.settings-toggle {
  margin-bottom: 16px;
}

.settings-panel {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.settings-panel.hidden {
  max-height: 0;
  opacity: 0;
  margin: 0;
  padding: 0;
}

/* Input groups */
.input-group {
  margin-bottom: 20px;
}

.input-group:last-child {
  margin-bottom: 0;
}

.input-group label {
  display: block;
  font-size: 13px;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: 6px;
}

.input-with-actions {
  display: flex;
  gap: 8px;
  align-items: stretch;
}

.input-with-actions input {
  flex: 1;
}

.input-group input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 14px;
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: inherit;
}

.input-group input:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(255, 0, 80, 0.1);
  background: var(--bg-card);
}

.input-group input::placeholder {
  color: var(--text-muted);
}

.input-group input[type="password"] {
  font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
  letter-spacing: 2px;
}

.input-group input[readonly] {
  background: var(--bg-secondary);
  color: var(--text-muted);
  cursor: not-allowed;
  border-color: var(--border-color);
}

.input-group input[readonly]:focus {
  border-color: var(--border-color);
  box-shadow: none;
}

/* Button styles */
.btn {
  padding: 12px 20px;
  border: none;
  border-radius: var(--radius-md);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  text-decoration: none;
  min-height: 44px;
  font-family: inherit;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

.btn:hover::before {
  width: 300px;
  height: 300px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

.btn:disabled::before {
  display: none;
}

.btn.primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  box-shadow: var(--shadow-md);
}

.btn.primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn.secondary {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn.secondary:hover:not(:disabled) {
  background: var(--bg-secondary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn.success {
  background: linear-gradient(135deg, var(--success-color), #059669);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn.success:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Icon buttons */
.btn-icon {
  width: 44px;
  height: 44px;
  padding: 0;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--bg-secondary);
  cursor: pointer;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  color: var(--text-secondary);
}

.btn-icon:hover {
  background: var(--bg-tertiary);
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-1px);
}

/* Help text */
.input-help {
  display: block;
  margin-top: 6px;
  font-size: 12px;
  color: var(--text-muted);
  line-height: 1.4;
}

.input-help a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
}

.input-help a:hover {
  text-decoration: underline;
}

/* Settings actions */
.settings-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-top: 20px;
}

/* Actions section */
.actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* Status indicators */
.status,
.connection-status {
  margin-top: 12px;
  padding: 12px 16px;
  border-radius: var(--radius-md);
  font-size: 13px;
  font-weight: 500;
  text-align: center;
  border: 1px solid;
}

.status.hidden,
.connection-status.hidden {
  display: none;
}

.status.success,
.connection-status.success {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
  border-color: rgba(16, 185, 129, 0.2);
}

.status.error,
.connection-status.error {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
  border-color: rgba(239, 68, 68, 0.2);
}

.status.loading,
.connection-status.loading {
  background: rgba(59, 130, 246, 0.1);
  color: var(--info-color);
  border-color: rgba(59, 130, 246, 0.2);
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Settings summary */
.settings-summary {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: 16px;
  margin-top: 16px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.summary-label {
  font-size: 13px;
  font-weight: 500;
  color: var(--text-secondary);
}

.summary-value {
  font-size: 13px;
  font-weight: 600;
  color: var(--text-primary);
}

.summary-value.configured {
  color: var(--success-color);
}

.summary-value.not-configured {
  color: var(--error-color);
}

.summary-value.empty {
  color: var(--text-muted);
  font-style: italic;
  font-weight: 400;
}

/* Data preview */
.data-container {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  background: var(--bg-secondary);
}

.no-data {
  padding: 32px 20px;
  text-align: center;
  color: var(--text-muted);
  font-size: 13px;
  font-style: italic;
}

.data-item {
  border-bottom: 1px solid var(--border-color);
  padding: 16px;
  background: var(--bg-card);
  transition: background-color 0.2s ease;
}

.data-item:hover {
  background: var(--bg-primary);
}

.data-item:last-child {
  border-bottom: none;
}

.data-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.data-type {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  font-size: 10px;
  font-weight: 700;
  padding: 4px 10px;
  border-radius: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.data-name {
  font-size: 13px;
  font-weight: 600;
  color: var(--text-primary);
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.data-details {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.data-field {
  font-size: 11px;
  color: var(--text-muted);
  background: var(--bg-tertiary);
  padding: 4px 8px;
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-color);
}

.data-field strong {
  color: var(--text-primary);
  font-weight: 600;
}

/* Footer */
.footer {
  padding: 16px 20px;
  background: var(--bg-secondary);
  text-align: center;
  border-top: 1px solid var(--border-color);
}

.footer small {
  color: var(--text-muted);
  font-size: 11px;
}

/* Scrollbar styles */
.data-container::-webkit-scrollbar {
  width: 6px;
}

.data-container::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: 3px;
}

.data-container::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.data-container::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.settings-panel:not(.hidden) {
  animation: slideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Focus styles for accessibility */
.btn:focus-visible,
.input-group input:focus-visible,
.btn-icon:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --border-color: #000000;
    --text-muted: #000000;
  }
  
  .container {
    border: 2px solid #000000;
  }
  
  .btn {
    border: 2px solid;
  }
  
  .input-group input {
    border: 2px solid #000000;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}