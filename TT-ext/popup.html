<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>TikTok Ads Extractor</title>
  <link rel="stylesheet" href="styles.css?v=2.2.0">
</head>
<body>
  <div class="container">
    <div class="header">
      <div class="header-title">
        <h1>🎯 TikTok Ads Extractor</h1>
        <span id="versionBadge" class="version-badge">v2.2.0</span>
      </div>
      <p>Витягування даних з TikTok Ads Manager</p>
    </div>

    <div class="settings">
      <h3>⚙️ Налаштування Airtable</h3>
      
      <div class="settings-toggle">
        <button id="toggleSettings" class="btn secondary">
          <span id="toggleText">Показати налаштування</span>
          <span id="toggleIcon">👁️</span>
        </button>
      </div>

      <div id="settingsPanel" class="settings-panel hidden">
        <div class="input-group">
          <label for="apiKey">API Key:</label>
          <div class="input-with-actions">
            <input type="password" id="apiKey" placeholder="Введіть Airtable API Key">
            <button id="toggleApiKey" class="btn-icon" title="Показати/Сховати">👁️</button>
            <button id="clearApiKey" class="btn-icon" title="Очистити">🗑️</button>
          </div>
          <small class="input-help">Отримайте на <a href="https://airtable.com/create/tokens" target="_blank">airtable.com/create/tokens</a></small>
        </div>
        
        <div class="input-group">
          <label for="tableUrl">🔗 Airtable URL (ПРОСТО ВСТАВТЕ ПОСИЛАННЯ!):</label>
          <div class="input-with-actions">
            <input type="text" id="tableUrl" placeholder="https://airtable.com/appXXXXXX/tblXXXXXX/... - ВСТАВТЕ І ВСЕ!">
            <button id="clearTableUrl" class="btn-icon" title="Очистити">🗑️</button>
          </div>
          <small class="input-help">🎯 <strong>ПРОСТО СКОПІЮЙТЕ URL З AIRTABLE І ВСТАВТЕ СЮДИ!</strong></small>
        </div>

        <!-- Приховані поля для автоматично витягнутих даних -->
        <input type="hidden" id="baseId">
        <input type="hidden" id="tableId">

        <div class="settings-actions">
          <button id="testConnection" class="btn secondary">🔍 Тест підключення</button>
          <button id="saveSettings" class="btn primary">💾 Зберегти</button>
        </div>

        <div class="settings-status">
          <div id="connectionStatus" class="connection-status hidden"></div>
        </div>
      </div>

      <div id="settingsSummary" class="settings-summary">
        <div class="summary-item">
          <span class="summary-label">Статус:</span>
          <span id="summaryStatus" class="summary-value">Не налаштовано</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">База:</span>
          <span id="summaryBase" class="summary-value">-</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">Таблиця:</span>
          <span id="summaryTable" class="summary-value">-</span>
        </div>
      </div>
    </div>

    <div class="actions">
      <h3>🚀 Дії</h3>
      
      <button id="extractData" class="btn secondary">📊 Витягти дані з поточної сторінки</button>
      <button id="sendToAirtable" class="btn success" disabled>📤 Відправити в Airtable</button>
      
      <div id="status" class="status hidden"></div>
    </div>

    <div class="data-preview">
      <h3>📋 Попередній перегляд даних</h3>
      <div id="dataContainer" class="data-container">
        <p class="no-data">Дані ще не витягнуті</p>
      </div>
    </div>

    <div class="footer">
      <small>Версія <span id="footerVersion">2.2.0</span> | Тільки для ads.tiktok.com</small>
    </div>
  </div>
  
  <script>
    // БЛОКУЄМО ВІДКРИТТЯ ЯК ОКРЕМОЇ СТОРІНКИ - НАХУЙ!
    if (window.location.protocol === 'chrome-extension:' &&
        window.location.pathname.includes('popup.html') &&
        !window.chrome?.extension?.getViews) {
      console.log('BLOCKED: popup.html opened as tab - CLOSING THIS SHIT!');
      document.body.innerHTML = '<div style="padding: 20px; text-align: center; color: #ff0050; font-family: Arial;"><h2>❌ НЕ ТАК!</h2><p>Натисніть на іконку розширення в панелі браузера!</p><p>Ця вкладка закриється через 2 секунди...</p></div>';
      setTimeout(() => window.close(), 2000);
    } else {
      // Тільки якщо це справжній popup - завантажуємо скрипт
      const script = document.createElement('script');
      script.src = 'popup.js?v=2.2.0';
      document.head.appendChild(script);
    }
  </script>
</body>
</html>